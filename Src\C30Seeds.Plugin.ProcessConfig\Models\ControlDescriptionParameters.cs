﻿using System.Globalization;

namespace C30Seeds.Plugin.ProcessConfig.Models;

/// <summary>
/// Represents a Data Transfer Object (DTO) that encapsulates all configurable parameters 
/// for generating a <controlDescription> XML fragment.
/// Nullable types (e.g., int?, bool?) are intentionally used to represent optional parameters. 
/// This allows the generator logic to differentiate between a provided value and an omitted one,
/// which in turn determines the value of the 'static' attribute in the output XML.
/// </summary>
public class ControlDescriptionParameters
{
    private string? _language;

    #region Mandatory Properties
    /// <summary>
    /// Gets or sets the target control's unique identifier, used in the 'forControl' attribute.
    /// </summary
    public string? ForControlGuid { get; set; }

    /// <summary>
    /// Gets or sets the ID of the primary custom control.
    /// </summary>
    public string? CustomControlId { get; set; }

    /// <summary>
    /// Gets or sets the logical name of the data field bound to the control.
    /// </summary>
    public string? DataFieldName { get; set; }
    #endregion


    #region Optional Configuration Properties
    /// <summary>
    /// Gets or sets the LCID (e.g., 1033, 1066).
    /// </summary>
    public int? LocaleId { get; set; }

    /// <summary>
    /// Gets or sets the language code (e.g., "en", "vi").
    /// </summary>
    public string? Language 
    {
        get
        {
            if (!string.IsNullOrEmpty(_language))
            {
                return _language;
            }

            if (LocaleId.HasValue)
            {
                try
                {
                    return LocaleId.Value switch
                    {
                        1033 => "en", // English - United States
                        1066 => "vi", // Vietnamese - Vietnam
                        1041 => "ja", // Japanese - Japan
                        1036 => "fr", // French - France
                        1031 => "de", // German - Germany
                        1049 => "ru", // Russian - Russia
                        2052 => "zh", // Chinese - Simplified
                        _ => new CultureInfo(LocaleId.Value).TwoLetterISOLanguageName
                    };
                }
                catch (CultureNotFoundException)
                {
                    return null;
                }
            }
            return null;
        }
        set
        {
            // Khi gán giá trị, lưu vào trường lưu trữ riêng
            _language = value;
        }
    }

    /// <summary>
    /// Gets or sets a nullable boolean to indicate if file attachments are enabled.
    /// A null value signifies that this parameter is not configured.
    /// </summary>
    public bool EnableFileAttachment { get; set; }

    /// <summary>
    /// Gets or sets a nullable boolean to indicate if file attachments are required.
    /// A null value signifies that this parameter is not configured.
    /// </summary>
    public bool RequireFileAttachment { get; set; }

    /// <summary>
    /// Gets or sets a string defining the allowed file types (e.g., "pdf,docx").
    /// An empty or null string signifies that this parameter is not configured.
    /// </summary>
    public string? AllowedFileTypes { get; set; }

    /// <summary>
    /// Gets or sets the maximum file size in megabytes.
    /// A null value signifies that this parameter is not configured.
    /// </summary>
    public int? MaxFileSizeInMB { get; set; }

    /// <summary>
    /// Gets or sets the raw JSON string for advanced UI customization.
    /// </summary>
    public string? CustomText { get; set; }
    #endregion
}
