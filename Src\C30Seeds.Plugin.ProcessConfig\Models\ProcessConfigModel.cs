﻿using System;

namespace C30Seeds.Plugin.ProcessConfig.Models;

public class ProcessConfigModel
{
    public string? StageName { get; set; }
    public Guid CallingUserId { get; set; }
    public int StateCode { get; set; }
    public int StatusCode { get; set; }
    public int ApprovalStatus { get; set; }
    public bool IsSaveInfoApprover { get; set; }
    public string? ApproverColumn { get; set; }
    public string? ApproveDateColumn { get; set; }
    public bool IsSaveStageName {  get; set; }
    public string? StageNameColumn { get; set; }
    public bool CheckDoc { get; set; }
    public bool FinalStep { get; set; }
}
