﻿namespace C30Seeds.Plugin.ProcessConfig.Define.Entities;

public static class ProcessConfig
{
    public const string LogicalName = "c30seeds_processconfig";
    public const string PrimaryKey = "c30seeds_processconfigid";
    public const string Name = "c30seeds_name";
    public const string Roles = "c30seeds_roles";
    public const string Process = "c30seeds_processid";

    public const string ProcessStage = "c30seeds_processstageid";
    public const string SetStateCode = "c30seeds_statecode";
    public const string SetStatusCode = "c30seeds_statuscode";
    public const string SetApprovalStatus = "c30seeds_approvalstatus";

    public const string ProcessPreviousStage = "c30seeds_processpreviousstageid";
    public const string SetPreviousStateCode = "c30seeds_statecodeprevious";
    public const string SetPreviousStatusCode = "c30seeds_statuscodeprevious";
    public const string SetPreviousApprovalStatus = "c30seeds_approvalstatusprevious";

    public const string WithMobile = "c30seeds_withmobile";
    public const string FinalStep = "c30seeds_finalstep";
    public const string CheckDoc = "c30seeds_checkdoc";

    public const string IsSaveInfoAprover = "c30seeds_isapproversaved";
    public const string ApproverColumn = "c30seeds_approvercol";
    public const string ApproveDateColumn = "c30seeds_approvedatecol";

    public const string IsSaveStageName = "c30seeds_isstagesaved";
    public const string StageNameColumn = "c30seeds_stagecol";

    public static class BusinessUnitConfigure
    {
        public const string Name = "c30seeds_buconfigure";
        public static class Value
        {
            public const int CurrentBusinessUnit = 100000000;
            public const int ParentBusinessUnit = 100000001;
            public const int SpecialBusinessUnit = 100000002;
        }
    }
    public const string ParentBusinessUnit = "c30seeds_parentbusinessunit";
    public const string SpecialBusinessUnit = "c30seeds_specialbusinessunit";
    public const string Registered = "c30seeds_registered";
    public const string BusinessProcessFlowStep = "c30seeds_bpfstep";
    public const string BusinessProcessFlowStepImage = "c30seeds_bpfstepimage";
    public const string MobileStep = "c30seeds_mobilestep";
    public const string MobileStepImage = "c30seeds_mobilestepimage";

    public static class PCF
    {
        public const string ControlId = "c30seeds_pcfcontrolid";
        public const string BoundField = "c30seeds_pcfboundfield";
        public const string Language = "c30seeds_pcflanguage";
        public const string EnableFileAttachment = "c30seeds_enablefileattachment";
        public const string MaxFileSize = "c30seeds_maxfilesize";
        public const string AllowedFileTypes = "c30seeds_allowedfiletypes";
        public const string RequireFileAttachment = "c30seeds_checkdoc";
        public const string CustomText = "c30seeds_pcfcustomtext";
    }
}
