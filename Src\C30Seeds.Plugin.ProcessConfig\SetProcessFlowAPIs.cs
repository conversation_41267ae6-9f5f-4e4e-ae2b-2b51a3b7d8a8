using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;

namespace C30Seeds.Plugin.ProcessConfig;

using Constants = Define.Constants;
using Entities = Define.Entities;
using Models;

/// <summary>
/// Custom API for SetProcessFlow operations
/// This API allows PCF controls to trigger process flow changes without relying on plugins
/// </summary>
public class SetProcessFlowAPIs : IPlugin
{
#if TRACE
    private ITracingService? _tracingService;
#endif

    /// <summary>
    /// Custom API Entry Point
    /// Input Parameters: Target (EntityReference), FromStage (EntityReference), ToStage (EntityReference)
    /// Output Parameters: Success (bool), Message (string), Details (string)
    /// </summary>
    public void Execute(IServiceProvider serviceProvider)
    {
#if TRACE
        _tracingService = (ITracingService)serviceProvider.GetService(typeof(ITracingService));
        ProcessFlowCommon.InitializeTracing(_tracingService);
#endif

        // Obtain the execution context from the service provider
        if (serviceProvider.GetService(typeof(IPluginExecutionContext)) is not IPluginExecutionContext context) return;

        // Obtain the organization service reference
        if (serviceProvider.GetService(typeof(IOrganizationServiceFactory)) is not IOrganizationServiceFactory factory) return;
        var service = factory.CreateOrganizationService(context.UserId);
        var serviceAdmin = factory.CreateOrganizationService(null);

        try
        {
            // Get input parameters
            var target = context.InputParameters.ContainsKey("Target") ? (EntityReference)context.InputParameters["Target"] : null;
            var fromStage = context.InputParameters.ContainsKey("FromStage") ? (EntityReference)context.InputParameters["FromStage"] : null;
            var toStage = context.InputParameters.ContainsKey("ToStage") ? (EntityReference)context.InputParameters["ToStage"] : null;

            if (target == null || fromStage == null || toStage == null)
            {
                SetOutputParameters(context, false, "Missing required parameters: Target, FromStage, or ToStage", "");
                return;
            }

#if TRACE
            Trace($"SetProcessFlowAPIs called - Target: {target.LogicalName}:{target.Id}, FromStage: {fromStage.Id}, ToStage: {toStage.Id}");
#endif

            var result = ExecuteProcessFlow(service, serviceAdmin, target, fromStage, toStage, context.UserId);
            SetOutputParameters(context, result.Success, result.Message, result.Data?.ToString() ?? "");
        }
        catch (Exception ex)
        {
#if TRACE
            Trace($"Error in SetProcessFlowAPIs.Execute: {ex.Message}");
#endif
            SetOutputParameters(context, false, $"Unexpected error: {ex.Message}", ex.StackTrace ?? "");
        }
    }

    private ProcessFlowResult ExecuteProcessFlow(IOrganizationService service, IOrganizationService serviceAdmin,
        EntityReference target, EntityReference fromStage, EntityReference toStage, Guid userId)
    {
        try
        {
            // Create a mock target entity with ActiveStage for validation
            var targetEntity = new Entity(target.LogicalName)
            {
                Id = target.Id,
                [Constants.CommonField.ActiveStage] = toStage
            };

            // Step 1: Validate process configuration and permissions
            var validationResult = ProcessFlowCommon.ValidateProcessConfig(service, serviceAdmin, targetEntity, userId, fromStage.Id);
            if (!validationResult.Success)
            {
                return validationResult;
            }

            var validationData = (ProcessFlowValidationData)validationResult.Data;
            var processConfigResults = validationData.ProcessConfigResults;
            var preRoleName = validationData.PreRoleName;

            // Step 2: Build process config model
            var model = ProcessFlowCommon.BuildProcessConfigModel(processConfigResults, userId);

            // Step 3: Get primary entity information
            var primaryEntityResult = ProcessFlowCommon.GetPrimaryEntityInfo(serviceAdmin, targetEntity);
            if (!primaryEntityResult.Success)
            {
                return primaryEntityResult;
            }

            var primaryEntityInfo = (PrimaryEntityInfo)primaryEntityResult.Data;

            // Step 4: Create update entity
            var updateEntity = new Entity(primaryEntityInfo.LogicalName)
            {
                Id = primaryEntityInfo.RecordId
            };

            // Step 5: Check document attachment if required
            var docCheckResult = ProcessFlowCommon.CheckDocumentAttachment(service, updateEntity.Id, model.CheckDoc);
            if (!docCheckResult.Success)
            {
                return docCheckResult;
            }

            // Step 6: Handle final step if required
            var finalStepResult = ProcessFlowCommon.HandleFinalStep(service, targetEntity, model.FinalStep);
            if (!finalStepResult.Success)
            {
                return finalStepResult;
            }

            // Step 7: Update entity with approver information and status
            var updateResult = ProcessFlowCommon.UpdateEntityWithApprover(updateEntity, model);
            if (!updateResult.Success)
            {
                return updateResult;
            }

            // Step 8: Change stage in BPF
            var changeStageResult = ProcessFlowCommon.ChangeStage(serviceAdmin, primaryEntityInfo.Retrieved.LogicalName,
                ref updateEntity, fromStage.Id, processConfigResults, model);
            if (!changeStageResult.Success)
            {
                return changeStageResult;
            }

            // Step 9: Update the entity
            service.Update(updateEntity);

            // Step 10: Get next step users and grant access
            var userIds = new List<Guid>();
            if (!preRoleName.ToLower().Equals(Constants.KeyForAllRole))
            {
                var roleName = processConfigResults.GetAttributeValue<string>(Entities.ProcessConfig.Roles);
                var businessUnitNextStep = Guid.Empty; // This would need to be implemented based on business unit configuration
                userIds = ProcessFlowCommon.GetNextStepUserIds(serviceAdmin, roleName, businessUnitNextStep);
            }

            if (userIds.Count > 0)
            {
                var grantAccessResult = ProcessFlowCommon.GrantUserAccess(serviceAdmin, updateEntity.ToEntityReference(), userIds);
                if (!grantAccessResult.Success)
                {
                    return grantAccessResult;
                }
            }

#if TRACE
            Trace($"Process flow completed successfully - Updated entity: {updateEntity.LogicalName}:{updateEntity.Id}");
#endif

            return ProcessFlowResult.CreateSuccess("Process flow completed successfully", new
            {
                UpdatedEntityId = updateEntity.Id,
                StageName = model.StageName,
                UsersGrantedAccess = userIds.Count
            });
        }
        catch (Exception ex)
        {
#if TRACE
            Trace($"Error in ExecuteProcessFlow: {ex.Message}");
#endif
            return ProcessFlowResult.CreateError($"Process flow execution error: {ex.Message}");
        }
    }

    private void SetOutputParameters(IPluginExecutionContext context, bool success, string message, string details)
    {
        context.OutputParameters["Success"] = success;
        context.OutputParameters["Message"] = message;
        context.OutputParameters["Details"] = details;

#if TRACE
        Trace($"Output - Success: {success}, Message: {message}");
#endif
    }

#if TRACE
    private void Trace(string format, params object[] args)
    {
        if (_tracingService is null)
        {
            Console.Write(format, args);
        }
        else
        {
            _tracingService.Trace(format, args);
        }
    }
#endif
}

/// <summary>
/// Request model for SetProcessFlow Custom API
/// </summary>
[DataContract]
public class SetProcessFlowRequest
{
    [DataMember]
    public EntityReference Target { get; set; }

    [DataMember]
    public EntityReference FromStage { get; set; }

    [DataMember]
    public EntityReference ToStage { get; set; }
}

/// <summary>
/// Response model for SetProcessFlow Custom API
/// </summary>
[DataContract]
public class SetProcessFlowResponse
{
    [DataMember]
    public bool Success { get; set; }

    [DataMember]
    public string Message { get; set; }

    [DataMember]
    public string Details { get; set; }
}
