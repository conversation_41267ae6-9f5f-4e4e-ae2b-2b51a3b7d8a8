(() => {

    var C30Seeds = window.C30Seeds || {};
    window.C30Seeds = C30Seeds;
    C30Seeds.ProcessConfig = C30Seeds.ProcessConfig || {};
    const Consts = {
        dialogSize: {
            height: 120,
            width: 450
        },
        apiDataType: {
            string: "Edm.String"
        },
        requestOperationType: {
            action: 0,
            "function": 1,
            crud: 2
        },
        responseKeys: {
            published: "Published!",
            unPublished: "Unpublished!"
        },
        action: {
            registrationServices: "c30seeds_RegistrationServices"
        },
        command: {
            publish: "Publish",
            unPublish: "UnPublish"
        },
        parameters: {
            request: "Request",
            response: "Response"
        },
        message: {
            loading: "Loading...",
            formContextNotFound: "formContext is null",
            confirmPublish: "Are you sure to publish this process config ?",
            confirmUnPublish: "Are you sure to un-publish this process config ?"
        },
        title: {
            information: "Information",
            confirm: "Confirm"
        },
        buttonText: {
            confirm: "Yes",
            yes: "Yes",
            no: "No",
            cancel: "Cancel"
        },
        xrmEnums: {
            requiredLevel: {
                none: "none",
                required: "required",
                recommended: "recommended"
            }
        },
        formTypes: {
            undefined: 0,
            create: 1,
            update: 2,
            readOnly: 3,
            disabled: 4
        },
        metadata: {
            logicalName: "c30seeds_processconfig",
            isSaveInfoAprover: {
                name: "c30seeds_isapproversaved",
                value: {
                    no: false,
                    yes: true
                }
            },
            approverColumn: "c30seeds_approvercol",
            approveDateColumn: "c30seeds_approvedatecol",
            isSaveStageName: {
                name: "c30seeds_isstagesaved",
                value: {
                    no: false,
                    yes: true
                }
            },
            stageNameColumn: "c30seeds_stagecol"
        }
    };

    function getFormContext(primaryControl) {
        if (primaryControl !== null) {
            if (typeof primaryControl.getAttribute === 'function') {
                return primaryControl;
            } else if (typeof primaryControl.getFormContext === 'function' && typeof (primaryControl.getFormContext()).getAttribute === 'function') {
                return primaryControl.getFormContext();
            }
        }
        return null;
    }

    function callActionWebApi(formContext, command) {
        const id = formContext.data.entity.getId();
        const reqObject = {};
        reqObject[Consts.parameters.request] = JSON.stringify({ Command: command, Data: id });
        reqObject.getMetadata = () => ({
                boundParameter: null,
                operationType: Consts.requestOperationType.action,
                operationName: Consts.action.registrationServices,
                parameterTypes: {
                    [Consts.parameters.request]: {
                        typeName: Consts.apiDataType.string,
                        structuralProperty: 1
                    }
                }
            });

        Xrm.WebApi.online.execute(reqObject).then(
            (response) => {
                Xrm.Utility.closeProgressIndicator();
                if (response?.ok) {
                    response.json().then((result) => {
                        Xrm.Navigation.openAlertDialog({ confirmButtonLabel: Consts.buttonText.confirm, text: result.Response, title: Consts.title.information }, { height: Consts.dialogSize.height, width: Consts.dialogSize.width }).then(
                            (_success) => {
                                if (result.Response === Consts.responseKeys.published || result.Response === Consts.responseKeys.unPublished) {
                                    formContext.data.refresh().then(() => {
                                        formContext.ui.refreshRibbon();
                                    });
                                }
                            }
                        );
                    });
                }
            },
            (error) => {
                Xrm.Utility.closeProgressIndicator();
                console.error(error.message);
            }
        );
    }

    C30Seeds.ProcessConfig.Ribbon = {
        publishClicked: (primaryControl) => {
            const formContext = getFormContext(primaryControl);
            if (formContext === null) {
                console.error(Consts.message.formContextNotFound);
                return;
            }
            try {
                Xrm.Navigation.openConfirmDialog({ text: Consts.message.confirmPublish, title: Consts.title.confirm }, { height: Consts.dialogSize.height, width: Consts.dialogSize.width }).then(
                    (success) => {
                        if (success.confirmed) {
                            Xrm.Utility.showProgressIndicator(Consts.message.loading);
                            callActionWebApi(formContext, Consts.command.publish);
                        }
                    }
                );
            } catch (e) {
                console.error(e.message);
            }
        },
        unPublishClicked: (primaryControl) => {
            const formContext = getFormContext(primaryControl);
            if (formContext === null) {
                console.error(Consts.message.formContextNotFound);
                return;
            }
            try {
                Xrm.Navigation.openConfirmDialog({ text: Consts.message.confirmUnPublish, title: Consts.title.confirm }, { height: Consts.dialogSize.height, width: Consts.dialogSize.width }).then(
                    (success) => {
                        if (success.confirmed) {
                            Xrm.Utility.showProgressIndicator(Consts.message.loading);
                            callActionWebApi(formContext, Consts.command.unPublish);
                        }
                    }
                );
            } catch (e) {
                console.error(e.message);
            }
        }
    };

    C30Seeds.ProcessConfig.Business = {
        showHideControl: (executionContext) => {
            const formContext = getFormContext(executionContext);
            const isSaveInfoAprover = formContext.getControl(Consts.metadata.isSaveInfoAprover.name);
            const approverColumn = formContext.getControl(Consts.metadata.approverColumn);
            const approveDateColumn = formContext.getControl(Consts.metadata.approveDateColumn);
            const isSaveStageName = formContext.getControl(Consts.metadata.isSaveStageName.name);
            const stageNameColumn = formContext.getControl(Consts.metadata.stageNameColumn);
            if (
                isSaveInfoAprover.getAttribute() === null ||
                isSaveInfoAprover.getAttribute().getValue() === null ||
                approverColumn.getAttribute() === null ||
                approveDateColumn.getAttribute() === null ||
                isSaveStageName.getAttribute() === null ||
                isSaveStageName.getAttribute().getValue() === null ||
                stageNameColumn.getAttribute() === null) {
                return
            }

            if (isSaveInfoAprover.getAttribute().getValue() !== Consts.metadata.isSaveInfoAprover.value.yes) {
                approverColumn.getAttribute().setValue(null)
                approveDateColumn.getAttribute().setValue(null)
            }
            approverColumn.setVisible(isSaveInfoAprover.getAttribute().getValue() === Consts.metadata.isSaveInfoAprover.value.yes)
            approveDateColumn.setVisible(isSaveInfoAprover.getAttribute().getValue() === Consts.metadata.isSaveInfoAprover.value.yes)

            if (isSaveStageName.getAttribute().getValue() === Consts.metadata.isSaveStageName.value.yes) {
                stageNameColumn.getAttribute().setRequiredLevel(Consts.xrmEnums.requiredLevel.required)
            } else {
                stageNameColumn.getAttribute().setValue(null)
                stageNameColumn.getAttribute().setRequiredLevel(Consts.xrmEnums.requiredLevel.none)
            }
            stageNameColumn.setVisible(isSaveStageName.getAttribute().getValue() === Consts.metadata.isSaveStageName.value.yes)
        }
    };

    C30Seeds.ProcessConfig.Form = {
        formOnLoad: (executionContext) => {
            const formContext = getFormContext(executionContext);
            if (formContext.ui.getFormType() !== Consts.formTypes.create) {
                C30Seeds.ProcessConfig.Business.showHideControl(executionContext);
            }
        },
        saveInfoApproverOnChange: (executionContext) => {
            C30Seeds.ProcessConfig.Business.showHideControl(executionContext);
        },
        saveStageNameOnChange: (executionContext) => {
            C30Seeds.ProcessConfig.Business.showHideControl(executionContext);
        }
    };

    // Expose constants for potential external use if needed, though generally discouraged for internal constants
    C30Seeds.ProcessConfig.Consts = Consts;

})();
