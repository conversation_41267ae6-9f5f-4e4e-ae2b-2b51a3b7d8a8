﻿using System.Xml;

namespace C30Seeds.Plugin.ProcessConfig;

using Define;
using Models;

/// <summary>
/// A static factory class for creating <controlDescription> XML fragments 
/// using the classic System.Xml.XmlElement (DOM) API.
/// </summary>
public static class XmlGenerator
{
    /// <summary>
    /// Creates a "simple" parameter element with only text content.
    /// Useful for special cases like <sourceControl>.
    /// </summary>
    /// <param name="xmlDoc">The parent XmlDocument instance required to create new elements.</param>
    /// <param name="elementName">The XML tag name for the parameter (e.g., "sourceControl").</param>
    /// <param name="content">The value for the parameter.</param>
    /// <returns>A new parameter XmlElement.</returns>
    private static XmlElement CreateSimpleParameterElement(XmlDocument xmlDoc, string elementName, string content)
    {
        XmlElement parameterElement = xmlDoc.CreateElement(elementName);
        parameterElement.InnerText = content ?? string.Empty;
        return parameterElement;
    }

    /// <summary>
    /// A private helper method to generate a single parameter XmlElement (e.g., <maxFileSize>).
    /// This method implements the core logic where the 'static' attribute is set to 'true' 
    /// if a meaningful value is provided, and 'false' otherwise.
    /// </summary>
    /// <param name="xmlDoc">The parent XmlDocument instance required to create new elements.</param>
    /// <param name="elementName">The XML tag name for the parameter (e.g., "maxFileSize").</param>
    /// <param name="elementType">The value for the 'type' attribute (e.g., "Whole.None").</param>
    /// <param name="parameterValue">The value for the parameter. Can be null or empty to indicate an omitted configuration.</param>
    /// <returns>A new parameter XmlElement.</returns>
    private static XmlElement CreateParameterElement(XmlDocument xmlDoc, string elementName, string elementType, object parameterValue)
    {
        bool isValueProvided = false;
        var content = string.Empty;

        if (parameterValue is string stringValue)
        {
            isValueProvided = !string.IsNullOrEmpty(stringValue);
            if (isValueProvided) content = stringValue;
        }
        else if (parameterValue != null) // Covers nullable types like int? and bool?
        {
            isValueProvided = true;
            // For booleans, convert to lowercase string representation as per XML requirement.
            content = (parameterValue is bool booleanValue) ? booleanValue.ToString().ToLower() : parameterValue.ToString();
        }

        // Create the element and set its attributes and content based on the logic above.
        XmlElement parameterElement = xmlDoc.CreateElement(elementName);
        parameterElement.SetAttribute("static", isValueProvided.ToString().ToLower());
        parameterElement.SetAttribute("type", elementType);
        parameterElement.InnerText = content;

        return parameterElement;
    }

    /// <summary>
    /// The main factory method that constructs a complete <controlDescription> XmlElement 
    /// based on the provided configuration parameters.
    /// </summary>
    /// <param name="xmlDoc">The parent XmlDocument instance required to create all child elements.</param>
    /// <param name="configParams">A DTO containing all the configuration values for the control description.</param>
    /// <returns>A new XmlElement representing the complete <controlDescription> fragment, ready to be appended to the main XML document.</returns>
    public static XmlElement CreateControlDescriptionElement(XmlDocument xmlDoc, ControlDescriptionParameters configParams)
    {
        // Create the root <controlDescription> element for this fragment.
        XmlElement controlDescriptionRoot = xmlDoc.CreateElement("controlDescription");
        controlDescriptionRoot.SetAttribute("forControl", configParams.ForControlGuid);

        // --- Block 1: The 'id-based' custom control, primarily for identification. ---
        XmlElement idBasedCustomControl = xmlDoc.CreateElement("customControl");
        idBasedCustomControl.SetAttribute("id", configParams.CustomControlId);

        XmlElement idParameters = xmlDoc.CreateElement("parameters");
        XmlElement datafieldnameElement = xmlDoc.CreateElement("datafieldname");
        datafieldnameElement.InnerText = configParams.DataFieldName;
        idParameters.AppendChild(datafieldnameElement);
        idBasedCustomControl.AppendChild(idParameters);

        // --- Block 2: The 'ProcessCheckpoint' custom control, containing detailed behavioral configuration. ---
        XmlElement processCheckpointCustomControl = xmlDoc.CreateElement("customControl");
        processCheckpointCustomControl.SetAttribute("name", Constants.CustomControlName);
        processCheckpointCustomControl.SetAttribute("formFactor", $"{Constants.FormFactor}");

        XmlElement checkpointParameters = xmlDoc.CreateElement("parameters");
        // 1. Handle the <sourceControl> element as a special case, creating a simple element.
        checkpointParameters.AppendChild(CreateSimpleParameterElement(xmlDoc, "sourceControl", configParams.DataFieldName!));
        // 2. Handle all other parameters using the "complex" helper method.
        checkpointParameters.AppendChild(CreateParameterElement(xmlDoc, "language", "SingleLine.Text", configParams.Language!));
        checkpointParameters.AppendChild(CreateParameterElement(xmlDoc, "enableFileAttachment", "TwoOptions", configParams.EnableFileAttachment));
        checkpointParameters.AppendChild(CreateParameterElement(xmlDoc, "requireFileAttachment", "TwoOptions", configParams.RequireFileAttachment));
        checkpointParameters.AppendChild(CreateParameterElement(xmlDoc, "allowedFileTypes", "SingleLine.Text", configParams.AllowedFileTypes!));
        checkpointParameters.AppendChild(CreateParameterElement(xmlDoc, "maxFileSize", "Whole.None", configParams.MaxFileSizeInMB!));
        checkpointParameters.AppendChild(CreateParameterElement(xmlDoc, "customText", "Multiple", configParams.CustomText!));
        processCheckpointCustomControl.AppendChild(checkpointParameters);

        // --- Assemble the final structure by appending the main blocks to the root. ---
        controlDescriptionRoot.AppendChild(idBasedCustomControl);
        controlDescriptionRoot.AppendChild(processCheckpointCustomControl);

        return controlDescriptionRoot;
    }
}
