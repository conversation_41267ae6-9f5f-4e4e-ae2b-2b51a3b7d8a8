﻿<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <MSBuildAllProjects Condition="'$(MSBuildVersion)' == '' Or '$(MSBuildVersion)' &lt; '16.0'">$(MSBuildAllProjects);$(MSBuildThisFileFullPath)</MSBuildAllProjects>
    <HasSharedItems>true</HasSharedItems>
    <SharedGUID>9f66a68f-5c78-4300-91ac-7af6b8cde0b2</SharedGUID>
  </PropertyGroup>
  <PropertyGroup Label="Configuration">
    <Import_RootNamespace>C30Seeds.Extension.PluginRegistration.Shared</Import_RootNamespace>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="$(MSBuildThisFileDirectory)Constants.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Entities\PluginAssembly.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Entities\PluginType.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Entities\SdkMessage.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Entities\SdkMessageFilter.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Entities\SdkMessageProcessingStep.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Entities\SdkMessageProcessingStepImage.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Message.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)PluginRegistration.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)QueryBuilder.cs" />
    <Compile Include="$(MSBuildThisFileDirectory)Replacements.cs" />
  </ItemGroup>
</Project>