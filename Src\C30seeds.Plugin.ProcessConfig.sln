﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.10.35201.131
MinimumVisualStudioVersion = 10.0.40219.1
Project("{D954291E-2A0B-460D-934E-DC6B0785DB48}") = "C30Seeds.Extension.PluginRegistration.Shared", "C30Seeds.Extension.PluginRegistration.Shared\C30Seeds.Extension.PluginRegistration.Shared.shproj", "{9F66A68F-5C78-4300-91AC-7AF6B8CDE0B2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "C30Seeds.Plugin.ProcessConfig", "C30Seeds.Plugin.ProcessConfig\C30Seeds.Plugin.ProcessConfig.csproj", "{77D45844-1A3F-47EC-AF3F-7BC64EFE484A}"
EndProject
Project("{E24C65DC-7377-472B-9ABA-BC803B73C61A}") = "C30Seeds.Plugin.ProcessConfig.WebResources", "C30Seeds.Plugin.ProcessConfig.WebResources\", "{9A2CF928-19BE-4F1B-826F-5D85570CE72C}"
	ProjectSection(WebsiteProperties) = preProject
		TargetFrameworkMoniker = ".NETFramework,Version%3Dv4.8.1"
		Debug.AspNetCompiler.VirtualPath = "/localhost_9315"
		Debug.AspNetCompiler.PhysicalPath = "C30Seeds.Plugin.ProcessConfig.WebResources\"
		Debug.AspNetCompiler.TargetPath = "PrecompiledWeb\localhost_9315\"
		Debug.AspNetCompiler.Updateable = "true"
		Debug.AspNetCompiler.ForceOverwrite = "true"
		Debug.AspNetCompiler.FixedNames = "false"
		Debug.AspNetCompiler.Debug = "True"
		Release.AspNetCompiler.VirtualPath = "/localhost_9315"
		Release.AspNetCompiler.PhysicalPath = "C30Seeds.Plugin.ProcessConfig.WebResources\"
		Release.AspNetCompiler.TargetPath = "PrecompiledWeb\localhost_9315\"
		Release.AspNetCompiler.Updateable = "true"
		Release.AspNetCompiler.ForceOverwrite = "true"
		Release.AspNetCompiler.FixedNames = "false"
		Release.AspNetCompiler.Debug = "False"
		VWDPort = "9315"
		SlnRelativePath = "C30Seeds.Plugin.ProcessConfig.WebResources\"
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{77D45844-1A3F-47EC-AF3F-7BC64EFE484A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{77D45844-1A3F-47EC-AF3F-7BC64EFE484A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{77D45844-1A3F-47EC-AF3F-7BC64EFE484A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{77D45844-1A3F-47EC-AF3F-7BC64EFE484A}.Release|Any CPU.Build.0 = Release|Any CPU
		{9A2CF928-19BE-4F1B-826F-5D85570CE72C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9A2CF928-19BE-4F1B-826F-5D85570CE72C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9A2CF928-19BE-4F1B-826F-5D85570CE72C}.Release|Any CPU.ActiveCfg = Debug|Any CPU
		{9A2CF928-19BE-4F1B-826F-5D85570CE72C}.Release|Any CPU.Build.0 = Debug|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {EDF7F824-8179-4EBE-823F-0A2476B22100}
	EndGlobalSection
	GlobalSection(SharedMSBuildProjectFiles) = preSolution
		C30Seeds.Extension.PluginRegistration.Shared\C30Seeds.Extension.PluginRegistration.Shared.projitems*{77d45844-1a3f-47ec-af3f-7bc64efe484a}*SharedItemsImports = 5
		C30Seeds.Extension.PluginRegistration.Shared\C30Seeds.Extension.PluginRegistration.Shared.projitems*{9f66a68f-5c78-4300-91ac-7af6b8cde0b2}*SharedItemsImports = 13
	EndGlobalSection
EndGlobal
