using Microsoft.Crm.Sdk.Messages;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;

using System;
using System.Collections.Generic;
using System.Linq;

namespace C30Seeds.Plugin.ProcessConfig;

using Constants = Define.Constants;
using Entities = Define.Entities;
using Models;

public static class ProcessFlowCommon
{
#if TRACE
    private static ITracingService? _tracingService;

    public static void InitializeTracing(ITracingService tracingService)
    {
        _tracingService = tracingService;
    }

    private static void Trace(string format, params object[] args)
    {
        if (_tracingService is null)
        {
            Console.Write(format, args);
        }
        else
        {
            _tracingService.Trace(format, args);
        }
    }
#endif

    public static ProcessFlowResult ValidateProcessConfig(IOrganizationService service, IOrganizationService serviceAdmin,
        Entity target, Guid userId, Guid preStageId)
    {
        try
        {
            var processConfig = service.RetrieveMultiple(new QueryBuilder().GetProcessConfigByStageId(preStageId)).Entities.ToList();
            var activeStageId = target.GetAttributeValue<EntityReference>(Constants.CommonField.ActiveStage)?.Id ?? Guid.Empty;
            var processConfigResults = service.RetrieveMultiple(new QueryBuilder().GetProcessConfigByStageId(activeStageId)).Entities.ToList();

            if (processConfig.Count == 0)
            {
                return ProcessFlowResult.CreateError(Constants.ErrorMessage.PreProcessNotYetConfigured);
            }
            if (processConfigResults.Count == 0)
            {
                return ProcessFlowResult.CreateError(Constants.ErrorMessage.ProcessNotYetConfigured);
            }
            if (processConfigResults.Count > 1 || processConfig.Count > 1)
            {
                return ProcessFlowResult.CreateError(Constants.ErrorMessage.DuplicateDetection);
            }

            var preRoleName = processConfig.First().GetAttributeValue<string>(Entities.ProcessConfig.Roles);

            // Check permissions
            if (!preRoleName.ToLower().Equals(Constants.KeyForAllRole))
            {
                var roleResults = serviceAdmin.RetrieveMultiple(new QueryBuilder().CheckRole(userId, preRoleName.Split(Constants.Separator))).Entities.ToList();
                if (roleResults.Count == 0)
                {
                    return ProcessFlowResult.CreateError(Constants.ErrorMessage.PermissionDenied);
                }
            }

            return ProcessFlowResult.CreateSuccess("Validation passed", new ProcessFlowValidationData
            {
                ProcessConfig = processConfig.First(),
                ProcessConfigResults = processConfigResults.First(),
                PreRoleName = preRoleName
            });
        }
        catch (Exception ex)
        {
#if TRACE
            Trace($"Error in ValidateProcessConfig: {ex.Message}");
#endif
            return ProcessFlowResult.CreateError($"Validation error: {ex.Message}");
        }
    }

    public static ProcessConfigModel BuildProcessConfigModel(Entity processConfigResults, Guid userId)
    {
        try
        {
            var model = new ProcessConfigModel
            {
                CallingUserId = userId,
                StageName = processConfigResults.GetAttributeValue<EntityReference>(Entities.ProcessConfig.ProcessStage).Name,
                StateCode = processConfigResults.GetAttributeValue<int>(Entities.ProcessConfig.SetStateCode),
                StatusCode = processConfigResults.GetAttributeValue<int>(Entities.ProcessConfig.SetStatusCode),
                ApprovalStatus = processConfigResults.Contains(Entities.ProcessConfig.SetApprovalStatus)
                    ? processConfigResults.GetAttributeValue<int>(Entities.ProcessConfig.SetApprovalStatus) : -1,
                IsSaveInfoApprover = processConfigResults.GetAttributeValue<bool>(Entities.ProcessConfig.IsSaveInfoAprover),
                ApproverColumn = processConfigResults.GetAttributeValue<string>(Entities.ProcessConfig.ApproverColumn),
                ApproveDateColumn = processConfigResults.GetAttributeValue<string>(Entities.ProcessConfig.ApproveDateColumn),
                IsSaveStageName = processConfigResults.GetAttributeValue<bool>(Entities.ProcessConfig.IsSaveStageName),
                StageNameColumn = processConfigResults.GetAttributeValue<string>(Entities.ProcessConfig.StageNameColumn),
                CheckDoc = processConfigResults.GetAttributeValue<bool>(Entities.ProcessConfig.CheckDoc),
                FinalStep = processConfigResults.GetAttributeValue<bool>(Entities.ProcessConfig.FinalStep)
            };

#if TRACE
            Trace($"Built ProcessConfigModel - StageName: {model.StageName}, StateCode: {model.StateCode}, StatusCode: {model.StatusCode}");
#endif

            return model;
        }
        catch (Exception ex)
        {
#if TRACE
            Trace($"Error in BuildProcessConfigModel: {ex.Message}");
#endif
            throw new Exception($"Error building process config model: {ex.Message}");
        }
    }

    public static ProcessFlowResult CheckDocumentAttachment(IOrganizationService service, Guid recordId, bool checkDoc)
    {
        try
        {
            if (!checkDoc) return ProcessFlowResult.CreateSuccess("Document check skipped");

            var results = service.RetrieveMultiple(new QueryBuilder().CheckAttachFileInNote(recordId)).Entities.ToList();
            if (results.Count == 0)
            {
                return ProcessFlowResult.CreateError(Constants.ErrorMessage.AttachFile);
            }

#if TRACE
            Trace($"Document check passed - Found {results.Count} attachments");
#endif

            return ProcessFlowResult.CreateSuccess("Document check passed");
        }
        catch (Exception ex)
        {
#if TRACE
            Trace($"Error in CheckDocumentAttachment: {ex.Message}");
#endif
            return ProcessFlowResult.CreateError($"Document check error: {ex.Message}");
        }
    }

    public static ProcessFlowResult GetPrimaryEntityInfo(IOrganizationService serviceAdmin, Entity target)
    {
        try
        {
            var logicalName = serviceAdmin.RetrieveMultiple(new QueryBuilder().GetPrimaryEntityName(target.LogicalName))
                .Entities.FirstOrDefault()?.GetAttributeValue<string>(Entities.Workflow.PrimaryEntity);

            if (string.IsNullOrWhiteSpace(logicalName))
            {
                return ProcessFlowResult.CreateError("Could not determine primary entity name");
            }

            var fieldId = Constants.Template.BusinessFlowProcessId.Replace(Constants.Replacement.EntityName, logicalName);
            var retrieved = serviceAdmin.Retrieve(target.LogicalName, target.Id, new ColumnSet(fieldId));
            var primaryRecordId = retrieved.GetAttributeValue<EntityReference>(fieldId)?.Id ?? Guid.Empty;

            if (primaryRecordId.Equals(Guid.Empty))
            {
                return ProcessFlowResult.CreateError("Primary record ID is empty");
            }

            // Check if record is active
            var refRecord = retrieved.GetAttributeValue<EntityReference>(fieldId);
            if (refRecord != null)
            {
                var record = serviceAdmin.Retrieve(refRecord.LogicalName, refRecord.Id, new ColumnSet(Constants.CommonField.StateCode));
                var stateCode = record.GetAttributeValue<OptionSetValue>(Constants.CommonField.StateCode)?.Value ?? -1;
                if (stateCode != -1 && stateCode.Equals(Constants.OptionSet.StateCode.InActive))
                {
                    return ProcessFlowResult.CreateError(Constants.ErrorMessage.RecordInactive);
                }
            }

#if TRACE
            Trace($"Primary entity info - LogicalName: {logicalName}, RecordId: {primaryRecordId}");
#endif

            return ProcessFlowResult.CreateSuccess("Primary entity info retrieved", new PrimaryEntityInfo
            {
                LogicalName = logicalName,
                RecordId = primaryRecordId,
                FieldId = fieldId,
                Retrieved = retrieved
            });
        }
        catch (Exception ex)
        {
#if TRACE
            Trace($"Error in GetPrimaryEntityInfo: {ex.Message}");
#endif
            return ProcessFlowResult.CreateError($"Primary entity error: {ex.Message}");
        }
    }

    public static ProcessFlowResult UpdateEntityWithApprover(Entity updateEntity, ProcessConfigModel model)
    {
        try
        {
            if (model.IsSaveInfoApprover)
            {
                if (!string.IsNullOrWhiteSpace(model.ApproverColumn))
                {
                    updateEntity[model.ApproverColumn] = new EntityReference(Entities.SystemUser.LogicalName, model.CallingUserId);
                }

                if (!string.IsNullOrWhiteSpace(model.ApproveDateColumn))
                {
                    updateEntity[model.ApproveDateColumn] = DateTime.UtcNow;
                }
            }

            // Set state and status codes
            updateEntity[Constants.CommonField.StateCode] = new OptionSetValue(model.StateCode);
            updateEntity[Constants.CommonField.StatusCode] = new OptionSetValue(model.StatusCode);

            if (model.ApprovalStatus > 0)
            {
                updateEntity[Constants.CommonField.ApprovalStatus] = new OptionSetValue(model.ApprovalStatus);
            }

            if (model.IsSaveStageName && !string.IsNullOrWhiteSpace(model.StageNameColumn) && !string.IsNullOrWhiteSpace(model.StageName))
            {
                updateEntity[model.StageNameColumn] = model.StageName;
            }

#if TRACE
            Trace($"Updated entity with approver info - IsSaveInfoApprover: {model.IsSaveInfoApprover}");
#endif

            return ProcessFlowResult.CreateSuccess("Entity updated with approver info");
        }
        catch (Exception ex)
        {
#if TRACE
            Trace($"Error in UpdateEntityWithApprover: {ex.Message}");
#endif
            return ProcessFlowResult.CreateError($"Update entity error: {ex.Message}");
        }
    }

    public static ProcessFlowResult HandleFinalStep(IOrganizationService service, Entity target, bool isFinalStep)
    {
        try
        {
            if (!isFinalStep) return ProcessFlowResult.CreateSuccess("Final step skipped");

            service.Update(new Entity(target.LogicalName)
            {
                Id = target.Id,
                [Constants.CommonField.StateCode] = new OptionSetValue(Constants.OptionSet.StateCode.InActive),
                [Constants.CommonField.StatusCode] = new OptionSetValue(Constants.OptionSet.BusinessProcessFlowStatusCode.Finished)
            });

#if TRACE
            Trace($"Final step processed - Process finished for {target.Id}");
#endif

            return ProcessFlowResult.CreateSuccess("Final step processed - Process finished");
        }
        catch (Exception ex)
        {
#if TRACE
            Trace($"Error in HandleFinalStep: {ex.Message}");
#endif
            return ProcessFlowResult.CreateError($"Final step error: {ex.Message}");
        }
    }

    public static ProcessFlowResult GrantUserAccess(IOrganizationService serviceAdmin, EntityReference targetRef, List<Guid> userIds)
    {
        try
        {
            for (var i = 0; i < userIds.Count; i++)
            {
                var grantAccessRequest = new GrantAccessRequest
                {
                    PrincipalAccess = new PrincipalAccess
                    {
                        AccessMask = Constants.ReadWriteShare,
                        Principal = new EntityReference(Entities.SystemUser.LogicalName, userIds[i])
                    },
                    Target = targetRef
                };

                serviceAdmin.Execute(grantAccessRequest);
            }

#if TRACE
            Trace($"Granted access to {userIds.Count} users");
#endif

            return ProcessFlowResult.CreateSuccess($"Granted access to {userIds.Count} users");
        }
        catch (Exception ex)
        {
#if TRACE
            Trace($"Error in GrantUserAccess: {ex.Message}");
#endif
            return ProcessFlowResult.CreateError($"Grant access error: {ex.Message}");
        }
    }

    public static List<Guid> GetNextStepUserIds(IOrganizationService serviceAdmin, string roleName, Guid businessUnitNextStep)
    {
        var userIds = new List<Guid>();
        try
        {
            if (!businessUnitNextStep.Equals(Guid.Empty))
            {
                userIds.AddRange(serviceAdmin.RetrieveMultiple(new QueryBuilder().FindUserByBuAndRole(businessUnitNextStep, roleName.Split(Constants.Separator)))
                    .Entities.ToList().Select(x => x.Id));
            }

#if TRACE
            Trace($"Found {userIds.Count} users for next step");
#endif

            return userIds;
        }
        catch (Exception ex)
        {
#if TRACE
            Trace($"Error in GetNextStepUserIds: {ex.Message}");
#endif
            return userIds;
        }
    }

    public static ProcessFlowResult ChangeStage(IOrganizationService service, string logicalName, ref Entity parentEntity,
        Guid preStageId, Entity config, ProcessConfigModel model)
    {
        try
        {
            // Get Process Instances
            if (service.Execute(new RetrieveProcessInstancesRequest
            {
                EntityId = parentEntity.Id,
                EntityLogicalName = parentEntity.LogicalName
            }) is not RetrieveProcessInstancesResponse processInstanceResponse)
            {
                return ProcessFlowResult.CreateError($"RetrieveProcessInstancesResponse: {Constants.ErrorMessage.ExecuteError}");
            }

            var activeProcessInstance = processInstanceResponse.Processes.Entities.First();
            var activeProcessInstanceId = activeProcessInstance.Id;
            var activeStageId = activeProcessInstance.GetAttributeValue<Guid>(Constants.CommonField.ProcessStageId);

            // Retrieve the process stages in the active path
            if (service.Execute(new RetrieveActivePathRequest
            {
                ProcessInstanceId = activeProcessInstanceId
            }) is not RetrieveActivePathResponse activePathResponse)
            {
                return ProcessFlowResult.CreateError($"RetrieveActivePathRequest: {Constants.ErrorMessage.ExecuteError}");
            }

            var preStageName = string.Empty;
            var activeStagePosition = -1;
            var preStagePosition = -1;

            for (var i = 0; i < activePathResponse.ProcessStages.Entities.Count; i++)
            {
                if (activePathResponse.ProcessStages.Entities[i].GetAttributeValue<Guid>(Constants.CommonField.ProcessStageId).Equals(activeStageId))
                {
                    activeStagePosition = i;
                }

                if (activePathResponse.ProcessStages.Entities[i].GetAttributeValue<Guid>(Constants.CommonField.ProcessStageId).Equals(preStageId))
                {
                    preStagePosition = i;
                    preStageName = activePathResponse.ProcessStages.Entities[i].GetAttributeValue<string>(Constants.CommonField.ProcessStageName);
                }
            }

            if (preStagePosition - activeStagePosition >= 1) // back process
            {
                var hasApprovalStatus = config.TryGetAttributeValue<int>(Entities.ProcessConfig.SetPreviousApprovalStatus, out var approvalStatus);
                var hasStateCode = config.TryGetAttributeValue<int>(Entities.ProcessConfig.SetPreviousStateCode, out var stateCode);
                var hasStatusCode = config.TryGetAttributeValue<int>(Entities.ProcessConfig.SetPreviousStatusCode, out var statusCode);

                if (hasApprovalStatus)
                {
                    parentEntity[Constants.CommonField.ApprovalStatus] = new OptionSetValue(approvalStatus);
                }

                if (hasStateCode)
                {
                    parentEntity[Constants.CommonField.StateCode] = new OptionSetValue(stateCode);
                }

                if (hasStatusCode)
                {
                    parentEntity[Constants.CommonField.StatusCode] = new OptionSetValue(statusCode);
                }

                if (model.IsSaveStageName && !string.IsNullOrWhiteSpace(model.StageNameColumn) && !string.IsNullOrWhiteSpace(preStageName))
                {
                    parentEntity[model.StageNameColumn] = preStageName;
                }

                // Retrieve the process instance record to update its active stage
                var retrievedProcessInstance = service.Retrieve(logicalName, activeProcessInstanceId, new ColumnSet(Constants.CommonField.ActiveStage));
                retrievedProcessInstance[Constants.CommonField.ActiveStage] = config[Entities.ProcessConfig.ProcessPreviousStage];
                service.Update(retrievedProcessInstance);

#if TRACE
                Trace($"Back process executed - moved from position {activeStagePosition} to {preStagePosition}");
#endif
            }

            return ProcessFlowResult.CreateSuccess("Stage change completed");
        }
        catch (Exception ex)
        {
#if TRACE
            Trace($"Error in ChangeStage: {ex.Message}");
#endif
            return ProcessFlowResult.CreateError($"Change stage error: {ex.Message}");
        }
    }
}

public class ProcessFlowResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public object? Data { get; set; }

    public static ProcessFlowResult CreateSuccess(string message, object? data = null)
    {
        return new ProcessFlowResult { Success = true, Message = message, Data = data };
    }

    public static ProcessFlowResult CreateError(string message)
    {
        return new ProcessFlowResult { Success = false, Message = message };
    }
}

public class ProcessFlowValidationData
{
    public Entity ProcessConfig { get; set; } = null!;
    public Entity ProcessConfigResults { get; set; } = null!;
    public string PreRoleName { get; set; } = string.Empty;
}

public class PrimaryEntityInfo
{
    public string LogicalName { get; set; } = string.Empty;
    public Guid RecordId { get; set; }
    public string FieldId { get; set; } = string.Empty;
    public Entity Retrieved { get; set; } = null!;
}
