﻿using Microsoft.Crm.Sdk.Messages;

namespace C30Seeds.Plugin.ProcessConfig.Define;

public static class Constants
{
    public const char Separator = ',';
    public const AccessRights ReadWriteShare = AccessRights.ReadAccess | AccessRights.WriteAccess | AccessRights.ShareAccess;
    public const string KeyForAllRole = "all";
    public const string AssemblyName = "C30Seeds.Plugin.ProcessConfig";
    public const string PluginTypeName = "C30Seeds.Plugin.ProcessConfig.SetProcessFlow";
    public const string PluginTypeNameForMobile = "C30Seeds.Plugin.ProcessConfig.SetProcessFlowFromMobile";
    public const string CustomControlName = "zlb_LokBiz.Controls.ProcessCheckpoint";
    public const int FormFactor = 2;

    public static class CommonField
    {
        public const string CreatedOn = "createdon";
        public const string CreatedBy = "createdby";
        public const string StateCode = "statecode";
        public const string StatusCode = "statuscode";
        public const string ApprovalStatus = "c30seeds_tinhtrangduyet";
        public const string OwnerId = "ownerid";
        public const string Name = "c30seeds_name";
        public const string ActiveStage = "activestageid";
        public const string StageId = "stageid";
        public const string ProcessStageId = "processstageid";
        public const string ProcessStageName = "stagename";
#if ENABLE_SET_STAGE_NAME
        public const string StageName = "c30seeds_stagename";
#endif
        public const string CallingUser = "CALLING_USER";
    }

    public static class FieldType
    {
        public const string Lookup = "EntityReference";
        public const string Memo = "Memo";
        public const string String = "String";
        public const string Decimal = "Decimal";
        public const string Int = "Int";
        public const string Int32 = "Int32";
        public const string Int64 = "Int64";
        public const string Boolean = "Boolean";
        public const string Double = "Double";
        public const string PickList = "OptionSetValue";
        public const string DateTime = "DateTime";
        public const string Money = "Money";
        public const string Guid = "Guid";
        public const string Byte = "Byte[]";
        public const string AliasedValue = "AliasedValue";
        public const string OptionSetValueCollection = "OptionSetValueCollection";
        public const string PartyList = "EntityCollection";
    }

    public static class OptionSet
    {
        public static class StatusCode
        {
            public const int Active = 1;
            public const int InActive = 2;
        }
        public static class StateCode
        {
            public const int Active = 0;
            public const int InActive = 1;
            public const int Canceled = 2;
        }
        public static class BusinessProcessFlowStatusCode
        {
            public const int Active = 1;
            public const int Finished = 2;
            public const int Aborted = 3;
        }
    }

    public static class Relationships
    {
        public const string SystemUserRoles = "systemuserroles";
        public const string TeamMemberShip = "teammembership";
    }

    public static class Parameters
    {
        public const string Target = "Target";
        public const string Assignee = "Assignee";
        public const string PreImage = "PreImage";
        public const string PostImage = "PostImage";
        public const string Relationship = "Relationship";
        public const string RelatedEntities = "RelatedEntities";
        public const string EntityMoniker = "EntityMoniker";
        public const string OutputResult = "Result";
        public const string Request = "Request";
        public const string Response = "Response";
        public const string BusinessEntityCollection = "BusinessEntityCollection";
        public const string Query = "Query";
    }

    public static class Message
    {
        public const string Assign = "Assign";
        public const string Create = "Create";
        public const string Update = "Update";
        public const string Delete = "Delete";
        public const string Qualify = "QualifyLead";
        public const string Associate = "Associate";
        public const string Disassociate = "Disassociate";
        public const string Get = "Get";
        public const string GetChild = "GetChild";
        public const string SetStateDynamicEntity = "SetStateDynamicEntity";
        public const string SetState = "SetState";
        public const string InputEntity = "InputEntity";
        public const string RetrieveMultiple = "RetrieveMultiple";
        public const string Retrieve = "Retrieve";
        public const string PortalServices = "c30seeds_PortalServices";
        public const string RegistrationServices = "c30seeds_RegistrationServices";
    }

    public static class ServiceCommand
    {
        public const string Publish = "publish";
        public const string UnPublish = "unpublish";
        public const string ApplyControl = "applycontrol";
        public const string ModifyControl = "modifycontrol";
        public const string RevertControl = "revertcontrol";
    }

    public static class RequestName
    {

    }

    public static class PluginStage
    {
        public const int PreValidation = 10;
        public const int PreOperation = 20;
        public const int PostOperation = 40;
    }

    public static class ErrorMessage
    {
        public const string PreProcessNotYetConfigured = @"This pre stage is not yet configured.";
        public const string ProcessNotYetConfigured = @"This active stage is not yet configured.";
        public const string PermissionDenied = "Bạn không có quyền duyệt!";
        public const string DuplicateDetection = @"Found multiple declarations of configure.";
        public const string AttachFile = "Please attach file before approve.";
        public const string RecordInactive = "Bản ghi đã bị hủy không thể duyệt!";
        public const string ProcessInstance = "Retrieved stages in the active path of the process instance:";
        public const string PluginConfigurationNull = "Unsecure or Secure plugin configuration cannot be null or empty for this plugin. It must represent entity name.";
        public const string ExistsPublished = "This process config has already published!";
        public const string Published = "Published!";
        public const string NotYetPublish = "This process config is draft";
        public const string Unpublished = "Unpublished!";
        public const string ExecuteError = "Execute error!";
        public const string ApplyControlSuccess = "Apply control successful!";
        public const string ApplyControlFailed = "Apply control failed. Please check your configuration and try again.";
        public const string ModifyControlSuccess = "Modify control successful!";
        public const string ModifyControlFailed = "Modify control failed. Please check your configuration and try again.";
        public const string RevertControlSuccess = "Revert control successful!";
        public const string RevertControlFailed = "Revert control failed. Please check your configuration and try again.";
        public const string Error = "Error!";
        public const string CanNotDelete = "This process config was publish. Can not deleted!";
        public const string WrongRecordId = "Wrong recordid!";
    }

    public static class RoleName
    {
        public const string SystemAdministrator = "System Administrator";
    }

    public static class Template
    {
        public static string BusinessFlowProcessId => $"bpf_{Replacement.EntityName}id";
    }

    public static class Replacement
    {
        public const string EntityName = "#EntityName#";
    }
}
