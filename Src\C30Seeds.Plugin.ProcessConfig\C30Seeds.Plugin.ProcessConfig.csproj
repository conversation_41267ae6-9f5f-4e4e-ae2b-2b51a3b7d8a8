﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net462</TargetFramework>
    <AssemblyOriginatorKeyFile Condition="Exists('c30seeds.snk')">c30seeds.snk</AssemblyOriginatorKeyFile>
    <SignAssembly Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">True</SignAssembly>
    <AssemblyName>C30Seeds.Plugin.ProcessConfig</AssemblyName>
    <RootNamespace>C30Seeds.Plugin.ProcessConfig</RootNamespace>
    <AssemblyVersion>1.0.0.0</AssemblyVersion>
    <FileVersion>1.0.0.0</FileVersion>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
    <PowerAppsTargetsPath>$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\PowerApps</PowerAppsTargetsPath>
    <ProjectTypeGuids>{4C25E9B5-9FA6-436c-8E19-B395D2A65FAF};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC};{77D45844-1A3F-47EC-AF3F-7BC64EFE484A}</ProjectTypeGuids>
    <NoWarn>IDE0290;</NoWarn>
  </PropertyGroup>

  <PropertyGroup>
    <DisableSetStageName>true</DisableSetStageName>
  </PropertyGroup>

  <Choose>
    <When Condition="'$(DisableSetStageName)'=='true'">
      <PropertyGroup>
        <DefineConstants>$(DefineConstants);</DefineConstants>
      </PropertyGroup>
    </When>
    <Otherwise>
      <PropertyGroup>
        <DefineConstants>$(DefineConstants);ENABLE_SET_STAGE_NAME;</DefineConstants>
      </PropertyGroup>
    </Otherwise>
  </Choose>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DefineConstants>$(DefineConstants);TRACE;</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DefineConstants>$(DefineConstants);</DefineConstants>
    <DebugSymbols>False</DebugSymbols>
    <DebugType>None</DebugType>
  </PropertyGroup>

  <Import Project="$(PowerAppsTargetsPath)\Microsoft.PowerApps.VisualStudio.Plugin.props" Condition="Exists('$(PowerAppsTargetsPath)\Microsoft.PowerApps.VisualStudio.Plugin.props')" />

  <ItemGroup>
    <Compile Remove="Define\Entities\PluginAssembly.cs" />
    <Compile Remove="Define\Entities\PluginType.cs" />
    <Compile Remove="Define\Entities\SdkMessage.cs" />
    <Compile Remove="Define\Entities\SdkMessageFilter.cs" />
    <Compile Remove="Define\Entities\SdkMessageProcessingStep.cs" />
    <Compile Remove="Define\Entities\SdkMessageProcessingStepImage.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.CrmSdk.CoreAssemblies" Version="9.0.2.*" PrivateAssets="All" />
    <PackageReference Include="Microsoft.PowerApps.MSBuild.Plugin" Version="1.*" PrivateAssets="All" />
    <PackageReference Include="Microsoft.NETFramework.ReferenceAssemblies" Version="1.0.3" PrivateAssets="All" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>

  <Import Project="..\C30Seeds.Extension.PluginRegistration.Shared\C30Seeds.Extension.PluginRegistration.Shared.projitems" Label="Shared" />

  <Import Project="$(PowerAppsTargetsPath)\Microsoft.PowerApps.VisualStudio.Plugin.targets" Condition="Exists('$(PowerAppsTargetsPath)\Microsoft.PowerApps.VisualStudio.Plugin.targets')" />
</Project>