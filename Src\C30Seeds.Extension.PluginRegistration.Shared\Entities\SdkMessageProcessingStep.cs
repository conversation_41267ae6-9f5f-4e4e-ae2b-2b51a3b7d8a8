﻿namespace C30Seeds.Extension.PluginRegistration.Shared.Entities;

public static class SdkMessageProcessingStep
{
    internal const string LogicalName = "sdkmessageprocessingstep";
    internal const string PrimaryKey = "sdkmessageprocessingstepid";
    internal const string Name = "name";
    internal const string Configuration = "configuration";
    internal const string Description = "description";
    internal const string SdkMessageId = "sdkmessageid";
    internal const string SdkMessageFilterId = "sdkmessagefilterid";
    internal const string PluginType = "plugintypeid";
    internal const string Rank = "rank";

    public static class InvocationSource
    {
        internal const string AttributeName = "invocationsource";

        public static class Value
        {
            public const int Internal = -1;
            public const int Parent = 0;
            public const int Child = 1;
        }
    }

    public static class Deployment
    {
        internal const string AttributeName = "supporteddeployment";

        public static class Value
        {
            public const int ServerOnly = 0;
            public const int ClientForOutlookOnly = 1;
            public const int Both = 2;
        }
    }

    public static class Mode
    {
        internal const string AttributeName = "mode";

        public static class Value
        {
            public const int Synchronous = 0;
            public const int Asynchronous = 1;
        }
    }

    public static class Stage
    {
        internal const string AttributeName = "stage";

        public static class Value
        {
            public const int PreValidation = 10;
            public const int PreOperation = 20;
            public const int PostOperation = 40;
        }
    }
}
