﻿namespace C30Seeds.Plugin.ProcessConfig.Define.Entities;

public static class SystemUser
{
    public const string LogicalName = "systemuser";
    public const string PrimaryKey = "systemuserid";
    public const string DateOfBirth = "c30seeds_dateofbirth";
    public const string DayOfBirth = "c30seeds_dayofbirth";
    public const string MonthOfBirth = "c30seeds_monthofbirth";
    public const string YearOfBirth = "c30seeds_yearofbirth";
    public const string FullName = "fullname";
    public const string MainBusinessUnit = "c30seeds_mainbusinessunit";
    public const string Department = "businessunitid";

    public static class IsDisabled
    {
        public const string Name = "isdisabled";
        public static class Value
        {
            public const bool Enable = false;
            public const bool Disable = true;
        }
    }
}
