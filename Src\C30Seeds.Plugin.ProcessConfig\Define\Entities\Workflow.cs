﻿namespace C30Seeds.Plugin.ProcessConfig.Define.Entities;

public static class Workflow
{
    public const string LogicalName = "workflow";
    public const string PrimaryKey = "workflowid";
    public const string Name = "name";
    public const string WorkFlowIdUnique = "workflowidunique";
    public const string UniqueName = "uniquename";
    public const string PrimaryEntity = "primaryentity";
    public const string RenderObjectTypeCode = "rendererobjecttypecode";
    public const string LanguageCode = "languagecode";

    public static class Type
    {
        public const string Name = "type";

        public static class Value
        {
            public const int Definition = 1;
            public const int Activation = 2;
            public const int Template = 3;
        }
    }

    public static class Category
    {
        public const string Name = "category";

        public static class Value
        {
            public const int Workflow = 0;
            public const int Dialog = 1;
            public const int BusinessRule = 2;
            public const int Action = 3;
            public const int BusinessProcessFlow = 4;
            public const int ModernFlow = 5;
            public const int Reserved = 6;
        }
    }
}
