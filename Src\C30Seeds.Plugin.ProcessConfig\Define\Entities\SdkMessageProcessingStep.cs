﻿namespace C30seeds.Plugin.ProcessConfig.Define.Entities;

public static class SdkMessageProcessingStep
{
    public const string LogicalName = "sdkmessageprocessingstep";
    public const string PrimaryKey = "sdkmessageprocessingstepid";
    public const string Name = "name";
    public const string Configuration = "configuration";
    public const string SdkMessageId = "sdkmessageid";
    public const string SdkMessageFilterId = "sdkmessagefilterid";
    public const string PluginType = "plugintypeid";
    public const string Rank = "rank";

    public static class InvocationSource
    {
        public const string AttributeName = "invocationsource";

        public static class Value
        {
            public const int Internal = -1;
            public const int Parent = 0;
            public const int Child = 1;
        }
    }

    public static class Deployment
    {
        public const string AttributeName = "supporteddeployment";

        public static class Value
        {
            public const int ServerOnly = 0;
            public const int ClientForOutlookOnly = 1;
            public const int Both = 2;
        }
    }

    public static class Mode
    {
        public const string AttributeName = "mode";

        public static class Value
        {
            public const int Synchronous = 0;
            public const int Asynchronous = 1;
        }
    }

    public static class Stage
    {
        public const string AttributeName = "stage";

        public static class Value
        {
            public const int PreValidation = 10;
            public const int PreOperation = 20;
            public const int PostOperation = 40;
        }
    }
}
