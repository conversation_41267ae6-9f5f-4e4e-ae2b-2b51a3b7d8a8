﻿using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;

using System;
using System.Linq;

namespace C30Seeds.Plugin.ProcessConfig;

using Constants = Define.Constants;
using Entities = Define.Entities;

public class Program : IPlugin
{
    public void Execute(IServiceProvider serviceProvider)
    {
        // Obtain the execution context from the service provider.
        if (serviceProvider.GetService(typeof(IPluginExecutionContext)) is not IPluginExecutionContext context) return;

        // Obtain the organization service reference.
        if (serviceProvider.GetService(typeof(IOrganizationServiceFactory)) is not IOrganizationServiceFactory factory) return;
        //var service = factory.CreateOrganizationService(context.UserId);
        var serviceAdmin = factory.CreateOrganizationService(null);
        if (context.Depth > 1) return;

        if (context.MessageName.ToLower().Equals(Constants.Message.Create.ToLower()))
        {
            // Obtain the target entity from the input parameters.
            if (context.InputParameters[Constants.Parameters.Target] is not Entity target) return;

            Main(serviceAdmin, target);
        }
        else if (context.MessageName.ToLower().Equals(Constants.Message.Delete.ToLower()))
        {
            if (context.InputParameters[Constants.Parameters.Target] is not EntityReference target) return;

            PreValidateConfigDelete(serviceAdmin, target);
        }
    }

    public void Main(IOrganizationService service, Entity target)
    {
        var results = service.RetrieveMultiple(new QueryBuilder().GetAllProcessById(target.GetAttributeValue<EntityReference>(Entities.ProcessConfig.Process)?.Id ?? Guid.Empty)).Entities.ToList();
        if (results.Count > 0)
        {
            target[Entities.ProcessConfig.Registered] = results.First().GetAttributeValue<bool>(Entities.ProcessConfig.Registered);
            target[Entities.ProcessConfig.WithMobile] = results.First().GetAttributeValue<bool>(Entities.ProcessConfig.WithMobile);
            target[Entities.ProcessConfig.BusinessProcessFlowStep] = results.First().GetAttributeValue<string>(Entities.ProcessConfig.BusinessProcessFlowStep);
            target[Entities.ProcessConfig.BusinessProcessFlowStepImage] = results.First().GetAttributeValue<string>(Entities.ProcessConfig.BusinessProcessFlowStepImage);
            target[Entities.ProcessConfig.MobileStep] = results.First().GetAttributeValue<string>(Entities.ProcessConfig.MobileStep);
            target[Entities.ProcessConfig.MobileStepImage] = results.First().GetAttributeValue<string>(Entities.ProcessConfig.MobileStepImage);
        }
    }

    public void PreValidateConfigDelete(IOrganizationService service, EntityReference target)
    {
        var retrieved = service.Retrieve(target.LogicalName, target.Id, new ColumnSet(Entities.ProcessConfig.Process, Entities.ProcessConfig.Registered));

        if (retrieved.GetAttributeValue<bool>(Entities.ProcessConfig.Registered))
        {
            throw new Exception(Constants.ErrorMessage.CanNotDelete);
        }
    }
}
