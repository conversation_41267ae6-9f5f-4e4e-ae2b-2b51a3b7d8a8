﻿using System;
using System.Linq;
using C30Seeds.Extension.PluginRegistration.Shared;
using C30Seeds.Extension.PluginRegistration.Shared.Entities;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Messages;
using Microsoft.Xrm.Sdk.Query;

namespace C30Seeds.Plugin.ProcessConfig;

using Models;
using Constants = Define.Constants;

public class RegistrationServices : IPlugin
{
    public void Execute(IServiceProvider serviceProvider)
    {
        // Obtain the execution context from the service provider.
        if (serviceProvider.GetService(typeof(IPluginExecutionContext)) is not IPluginExecutionContext context) return;

        if (context.InputParameters[Constants.Parameters.Request] is not string request) return;

        if (string.IsNullOrWhiteSpace(request)) return;

        var reqModel = Helper.DeSerializeObject<RequestModel>(request);
        if (reqModel == null) return;

        if (reqModel.Data != null &&
            !string.IsNullOrWhiteSpace(reqModel.Data))
        {
            reqModel.Data = Uri.UnescapeDataString(reqModel.Data);
        }

        // Obtain the organization service reference.
        if (serviceProvider.GetService(typeof(IOrganizationServiceFactory)) is not IOrganizationServiceFactory factory) return;

        var serviceAdmin = factory.CreateOrganizationService(null);

        try
        {
            var message = string.Empty;
            if (context.MessageName.Equals(Constants.Message.RegistrationServices))
            {
                message = Main(serviceAdmin, reqModel);
            }

            context.OutputParameters[Constants.Parameters.Response] = message;
        }
        catch (Exception e)
        {
            throw new InvalidPluginExecutionException(e.Message);
        }
    }

    public string Main(IOrganizationService service, RequestModel request)
    {
        return request.Command.ToLowerInvariant() switch
        {
            Constants.ServiceCommand.Publish => Publish(service, request.Data),
            Constants.ServiceCommand.UnPublish => UnPublish(service, request.Data),
            Constants.ServiceCommand.ApplyControl => ApplyControl(service, request.Data),
            Constants.ServiceCommand.ModifyControl => ModifyControl(service, request.Data),
            Constants.ServiceCommand.RevertControl => RevertControl(service, request.Data),
            _ => $"{Constants.ErrorMessage.Error}: {request.Command} | {request.Data}"
        };
    }


    public string Publish(IOrganizationService service, string data)
    {
        if (!Guid.TryParse(data, out var id))
        {
            return Constants.ErrorMessage.WrongRecordId;
        }
        var retrieved = service.Retrieve(Define.Entities.ProcessConfig.LogicalName, id, new ColumnSet(Define.Entities.ProcessConfig.Process, Define.Entities.ProcessConfig.ProcessStage, Define.Entities.ProcessConfig.PCF.BoundField, Define.Entities.ProcessConfig.PCF.Language, Define.Entities.ProcessConfig.PCF.EnableFileAttachment, Define.Entities.ProcessConfig.PCF.MaxFileSize, Define.Entities.ProcessConfig.PCF.AllowedFileTypes, Define.Entities.ProcessConfig.PCF.RequireFileAttachment, Define.Entities.ProcessConfig.PCF.CustomText, Define.Entities.ProcessConfig.WithMobile, Define.Entities.ProcessConfig.Registered));

        if (retrieved.GetAttributeValue<bool>(Define.Entities.ProcessConfig.Registered))
        {
            return Constants.ErrorMessage.ExistsPublished;
        }

        var processId = retrieved.GetAttributeValue<EntityReference>(Define.Entities.ProcessConfig.Process)?.Id ?? Guid.Empty;
        var results = service.RetrieveMultiple(new QueryBuilder().GetAllProcessById(processId)).Entities.ToList();
        var multipleRequest = new ExecuteMultipleRequest()
        {
            Settings = new ExecuteMultipleSettings()
            {
                ContinueOnError = true,
                ReturnResponses = true
            },
            Requests = []
        };

        var update = new Entity(retrieved.LogicalName) {Id = retrieved.Id};
        var uniqueName = service.RetrieveMultiple(new QueryBuilder().GetWorkflowUniqueName(processId)).Entities.FirstOrDefault()?.GetAttributeValue<string>(Define.Entities.Workflow.UniqueName) ?? string.Empty;

        var registration = new PluginRegistration(service);
        

        var stepId = registration.CreateSdkMessageStep(Constants.AssemblyName, Constants.PluginTypeName, Constants.Message.Update, uniqueName, SdkMessageProcessingStep.Mode.Value.Synchronous, SdkMessageProcessingStep.Stage.Value.PostOperation, 1);

        if (stepId.Equals(Guid.Empty)) goto THE_END;

        update[Define.Entities.ProcessConfig.BusinessProcessFlowStep] = $"{stepId}";

        var imageId = registration.CreateImage(stepId, SdkMessageProcessingStepImage.ImageType.Value.PreImage, Constants.Parameters.PreImage);

        if (imageId.Equals(Guid.Empty))
        {
            service.Delete(SdkMessageProcessingStep.LogicalName, stepId);
            goto THE_END;
        }

        update[Define.Entities.ProcessConfig.BusinessProcessFlowStepImage] = $"{imageId}";

        if (retrieved.GetAttributeValue<bool>(Define.Entities.ProcessConfig.WithMobile))
        {
            var logicalName = service.RetrieveMultiple(new QueryBuilder().GetPrimaryEntityName(uniqueName)).Entities.FirstOrDefault()?.GetAttributeValue<string>(Define.Entities.Workflow.PrimaryEntity);

            var mobileStepId = registration.CreateSdkMessageStep(Constants.AssemblyName, Constants.PluginTypeNameForMobile, Constants.Message.Update, logicalName!, SdkMessageProcessingStep.Mode.Value.Synchronous, SdkMessageProcessingStep.Stage.Value.PostOperation, 1, uniqueName);

            if (mobileStepId.Equals(Guid.Empty)) goto THE_END;

            update[Define.Entities.ProcessConfig.MobileStep] = $"{mobileStepId}";

            var mobileImageId = registration.CreateImage(mobileStepId, SdkMessageProcessingStepImage.ImageType.Value.PreImage, Constants.Parameters.PreImage, Constants.CommonField.StateCode, Constants.CommonField.StatusCode);

            if (mobileImageId.Equals(Guid.Empty))
            {
                service.Delete(SdkMessageProcessingStep.LogicalName, mobileStepId);
                goto THE_END;
            }
            update[Define.Entities.ProcessConfig.MobileStepImage] = $"{mobileImageId}";
            update[Define.Entities.ProcessConfig.Registered] = !stepId.Equals(Guid.Empty) && !imageId.Equals(Guid.Empty) && !mobileStepId.Equals(Guid.Empty) && !mobileImageId.Equals(Guid.Empty);
        }
        else
        {
            update[Define.Entities.ProcessConfig.Registered] = !stepId.Equals(Guid.Empty) && !imageId.Equals(Guid.Empty);
        }

        for (var i = 0; i < results.Count; i++)
        {
            var updateRequest = new UpdateRequest
            {
                Target = new Entity(Define.Entities.ProcessConfig.LogicalName)
                {
                    Id = results[i].Id,
                    [Define.Entities.ProcessConfig.Registered] = update.GetAttributeValue<bool>(Define.Entities.ProcessConfig.Registered),
                    [Define.Entities.ProcessConfig.WithMobile] = retrieved.GetAttributeValue<bool>(Define.Entities.ProcessConfig.WithMobile),
                    [Define.Entities.ProcessConfig.BusinessProcessFlowStep] = update.GetAttributeValue<string>(Define.Entities.ProcessConfig.BusinessProcessFlowStep),
                    [Define.Entities.ProcessConfig.BusinessProcessFlowStepImage] = update.GetAttributeValue<string>(Define.Entities.ProcessConfig.BusinessProcessFlowStepImage),
                    [Define.Entities.ProcessConfig.MobileStep] = update.GetAttributeValue<string>(Define.Entities.ProcessConfig.MobileStep),
                    [Define.Entities.ProcessConfig.MobileStepImage] = update.GetAttributeValue<string>(Define.Entities.ProcessConfig.MobileStepImage)
                }
            };
            multipleRequest.Requests.Add(updateRequest);
        }

        if (service.Execute(multipleRequest) is not ExecuteMultipleResponse multipleResponse)
        {
            return Constants.ErrorMessage.ExecuteError;
        }
        if (multipleResponse.Responses.Count > 0 && update.GetAttributeValue<bool>(Define.Entities.ProcessConfig.Registered))
        {
            return Constants.ErrorMessage.Published;
        }

        THE_END:
        return Constants.ErrorMessage.Error;
    }

    private string UnPublish(IOrganizationService service, string data)
    {
        if (!Guid.TryParse(data, out var id))
            return Constants.ErrorMessage.WrongRecordId;
        var retrieved = service.Retrieve(Define.Entities.ProcessConfig.LogicalName, id, new ColumnSet(Define.Entities.ProcessConfig.Registered, Define.Entities.ProcessConfig.Process, Define.Entities.ProcessConfig.ProcessStage, Define.Entities.ProcessConfig.PCF.BoundField, Define.Entities.ProcessConfig.BusinessProcessFlowStep, Define.Entities.ProcessConfig.BusinessProcessFlowStepImage, Define.Entities.ProcessConfig.MobileStep, Define.Entities.ProcessConfig.MobileStepImage));
        var processId = retrieved.GetAttributeValue<EntityReference>(Define.Entities.ProcessConfig.Process)?.Id ?? Guid.Empty;
        var uniqueName = service.RetrieveMultiple(new QueryBuilder().GetWorkflowUniqueName(processId)).Entities.FirstOrDefault()?.GetAttributeValue<string>(Define.Entities.Workflow.UniqueName) ?? string.Empty;

        if (!retrieved.GetAttributeValue<bool>(Define.Entities.ProcessConfig.Registered))
        {
            return Constants.ErrorMessage.NotYetPublish;
        }

        var results = service.RetrieveMultiple(new QueryBuilder().GetAllProcessById(processId)).Entities.ToList();
        var multipleRequest = new ExecuteMultipleRequest()
        {
            Settings = new ExecuteMultipleSettings()
            {
                ContinueOnError = true,
                ReturnResponses = true
            },
            Requests = []
        };

        Guid.TryParse(retrieved.GetAttributeValue<string>(Define.Entities.ProcessConfig.BusinessProcessFlowStep), out var stepId);
        Guid.TryParse(retrieved.GetAttributeValue<string>(Define.Entities.ProcessConfig.BusinessProcessFlowStepImage), out var imageId);
        Guid.TryParse(retrieved.GetAttributeValue<string>(Define.Entities.ProcessConfig.MobileStep), out var mobileStepId);
        Guid.TryParse(retrieved.GetAttributeValue<string>(Define.Entities.ProcessConfig.MobileStepImage), out var mobileImageId);

        if (!imageId.Equals(Guid.Empty))
        {
            service.Delete(SdkMessageProcessingStepImage.LogicalName, imageId);
        }

        if (!stepId.Equals(Guid.Empty))
        {
            service.Delete(SdkMessageProcessingStep.LogicalName, stepId);
        }

        if (!mobileImageId.Equals(Guid.Empty))
        {
            service.Delete(SdkMessageProcessingStepImage.LogicalName, mobileImageId);
        }

        if (!mobileStepId.Equals(Guid.Empty))
        {
            service.Delete(SdkMessageProcessingStep.LogicalName, mobileStepId);
        }

        for (var i = 0; i < results.Count; i++)
        {
            var updateRequest = new UpdateRequest
            {
                Target = new Entity(Define.Entities.ProcessConfig.LogicalName)
                {
                    Id = results[i].Id,
                    [Define.Entities.ProcessConfig.Registered] = false,
                    [Define.Entities.ProcessConfig.BusinessProcessFlowStep] = string.Empty,
                    [Define.Entities.ProcessConfig.BusinessProcessFlowStepImage] = string.Empty,
                    [Define.Entities.ProcessConfig.MobileStep] = string.Empty,
                    [Define.Entities.ProcessConfig.MobileStepImage] = string.Empty
                }
            };
            multipleRequest.Requests.Add(updateRequest);
        }

        //service.Update(new Entity(retrieved.LogicalName)
        //{
        //    Id = retrieved.Id,
        //    [Define.Entities.ProcessConfig.Registered] = false
        //});

        if (service.Execute(multipleRequest) is not ExecuteMultipleResponse multipleResponse)
        {
            return Constants.ErrorMessage.ExecuteError;
        }

        return multipleResponse.Responses.Count <= 0 ? Constants.ErrorMessage.Error : Constants.ErrorMessage.Unpublished;
    }

    private string ApplyControl(IOrganizationService service, string data)
    {
        if (!Guid.TryParse(data, out var id))
        {
            return Constants.ErrorMessage.WrongRecordId;
        }
        var multipleRequest = new ExecuteMultipleRequest()
        {
            Settings = new ExecuteMultipleSettings()
            {
                ContinueOnError = true,
                ReturnResponses = true
            },
            Requests = []
        };
        var retrieved = service.Retrieve(Define.Entities.ProcessConfig.LogicalName, id, new ColumnSet(Define.Entities.ProcessConfig.Process, Define.Entities.ProcessConfig.ProcessStage, Define.Entities.ProcessConfig.PCF.ControlId, Define.Entities.ProcessConfig.PCF.BoundField, Define.Entities.ProcessConfig.PCF.Language, Define.Entities.ProcessConfig.PCF.EnableFileAttachment, Define.Entities.ProcessConfig.PCF.MaxFileSize, Define.Entities.ProcessConfig.PCF.AllowedFileTypes, Define.Entities.ProcessConfig.PCF.RequireFileAttachment, Define.Entities.ProcessConfig.PCF.CustomText));
        var processId = retrieved.GetAttributeValue<EntityReference>(Define.Entities.ProcessConfig.Process)?.Id ?? Guid.Empty;
        var uniqueName = service.RetrieveMultiple(new QueryBuilder().GetWorkflowUniqueName(processId)).Entities.FirstOrDefault()?.GetAttributeValue<string>(Define.Entities.Workflow.UniqueName) ?? string.Empty;
        var bpfService = new BusinessProcessFlowFormServices(service, multipleRequest, retrieved);
        bpfService.ApplyControl(uniqueName);
        if (service.Execute(multipleRequest) is not ExecuteMultipleResponse multipleResponse)
        {
            return Constants.ErrorMessage.ApplyControlFailed;
        }
        if (multipleResponse.Responses.Count > 0)
        {
            return Constants.ErrorMessage.ApplyControlSuccess;
        }

        return Constants.ErrorMessage.Error;
    }

    private string ModifyControl(IOrganizationService service, string data)
    {
        if (!Guid.TryParse(data, out var id))
        {
            return Constants.ErrorMessage.WrongRecordId;
        }
        var multipleRequest = new ExecuteMultipleRequest()
        {
            Settings = new ExecuteMultipleSettings()
            {
                ContinueOnError = true,
                ReturnResponses = true
            },
            Requests = []
        };
        var retrieved = service.Retrieve(Define.Entities.ProcessConfig.LogicalName, id, new ColumnSet(Define.Entities.ProcessConfig.Process, Define.Entities.ProcessConfig.ProcessStage, Define.Entities.ProcessConfig.PCF.ControlId, Define.Entities.ProcessConfig.PCF.BoundField, Define.Entities.ProcessConfig.PCF.Language, Define.Entities.ProcessConfig.PCF.EnableFileAttachment, Define.Entities.ProcessConfig.PCF.MaxFileSize, Define.Entities.ProcessConfig.PCF.AllowedFileTypes, Define.Entities.ProcessConfig.PCF.RequireFileAttachment, Define.Entities.ProcessConfig.PCF.CustomText));
        var processId = retrieved.GetAttributeValue<EntityReference>(Define.Entities.ProcessConfig.Process)?.Id ?? Guid.Empty;
        var uniqueName = service.RetrieveMultiple(new QueryBuilder().GetWorkflowUniqueName(processId)).Entities.FirstOrDefault()?.GetAttributeValue<string>(Define.Entities.Workflow.UniqueName) ?? string.Empty;
        var bpfService = new BusinessProcessFlowFormServices(service, multipleRequest, retrieved);
        bpfService.ApplyControl(uniqueName);
        if (service.Execute(multipleRequest) is not ExecuteMultipleResponse multipleResponse)
        {
            return Constants.ErrorMessage.ModifyControlFailed;
        }
        if (multipleResponse.Responses.Count > 0)
        {
            return Constants.ErrorMessage.ModifyControlSuccess;
        }

        return Constants.ErrorMessage.Error;
    }

    private string RevertControl(IOrganizationService service, string data)
    {
        if (!Guid.TryParse(data, out var id))
        {
            return Constants.ErrorMessage.WrongRecordId;
        }
        var multipleRequest = new ExecuteMultipleRequest()
        {
            Settings = new ExecuteMultipleSettings()
            {
                ContinueOnError = true,
                ReturnResponses = true
            },
            Requests = []
        };
        var retrieved = service.Retrieve(Define.Entities.ProcessConfig.LogicalName, id, new ColumnSet(Define.Entities.ProcessConfig.Process, Define.Entities.ProcessConfig.ProcessStage, Define.Entities.ProcessConfig.PCF.BoundField));
        var processId = retrieved.GetAttributeValue<EntityReference>(Define.Entities.ProcessConfig.Process)?.Id ?? Guid.Empty;
        var uniqueName = service.RetrieveMultiple(new QueryBuilder().GetWorkflowUniqueName(processId)).Entities.FirstOrDefault()?.GetAttributeValue<string>(Define.Entities.Workflow.UniqueName) ?? string.Empty;
        var bpfService = new BusinessProcessFlowFormServices(service, multipleRequest, retrieved);
        bpfService.RevertControl(uniqueName);
        if (service.Execute(multipleRequest) is not ExecuteMultipleResponse multipleResponse)
        {
            return Constants.ErrorMessage.RevertControlFailed;
        }
        if (multipleResponse.Responses.Count > 0)
        {
            return Constants.ErrorMessage.RevertControlSuccess;
        }

        return Constants.ErrorMessage.Error;
    }
}
