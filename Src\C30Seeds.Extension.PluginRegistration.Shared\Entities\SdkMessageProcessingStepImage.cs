﻿namespace C30Seeds.Extension.PluginRegistration.Shared.Entities;

public static class SdkMessageProcessingStepImage
{
    internal const string LogicalName = "sdkmessageprocessingstepimage";
    internal const string PrimaryKey = "sdkmessageprocessingstepimageid";
    internal const string Name = "name";
    internal const string MessagePropertyName = "messagepropertyname";
    internal const string EntityAlias = "entityalias";
    internal const string Attributes = "attributes";
    internal const string MessageProcessingStep = "sdkmessageprocessingstepid";

    public static class ImageType
    {
        internal const string AttributeName = "imagetype";

        public static class Value
        {
            public const int PreImage = 0;
            public const int PostImage = 1;
            public const int Both = 2;
        }
    }
}
