﻿using System.Collections.Generic;

namespace C30Seeds.Extension.PluginRegistration.Shared;

internal static class Message
{
    public const int Ok = 0;

    public const int AssemblyNotFound = 100000000;
    public const int SdkMessageNotFound = 100000001;
    public const int SdkMessageFilterNotFound = 100000002;

    public static Dictionary<int, string> GetMessage =>
        new()
        {
            { AssemblyNotFound, $"Assembly {Replacements.EntityName} not found." },
            { SdkMessageNotFound, $"SDK Message Name {Replacements.MessageName} not found." },
            { SdkMessageFilterNotFound, $"SDK Message Filter {Replacements.EntityName} not found." }
        };
}
