﻿namespace C30Seeds.Extension.PluginRegistration.Shared;

internal static class Constants
{
    internal const string AnyEntity = "any entity";

    internal static class DefaultValues
    {
        internal const string MessagePropertyName = "Target";
    }

    internal static class Template
    {
        internal static string Description => $"{Replacements.PluginTypeName} : {Replacements.MessageName} of {Replacements.EntityName}";
    }
}
