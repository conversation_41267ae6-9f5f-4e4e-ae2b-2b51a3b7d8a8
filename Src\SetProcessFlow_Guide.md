# SetProcessFlow.cs - Hướng Dẫn và Định Hướng

## **Tổng Quan**
Plugin `SetProcessFlow` được thiết kế để quản lý Business Process Flow (BPF) trong Microsoft Dynamics 365, tự động hóa việc chuyển stage và cập nhật trạng thái record với kiểm soát quyền chặt chẽ.

## **Bảng Flow Logic**

| **Bước** | **Phương thức** | **Mô tả** | **Điều kiện** |
|----------|-----------------|-----------|---------------|
| 1 | `Execute()` | Khởi tạo plugin và kiểm tra điều kiện cơ bản | - Message = "Update"<br>- Target chứa ActiveStage<br>- Depth ≤ 1 |
| 2 | `Main()` | L<PERSON>y và validate process config | - <PERSON><PERSON><PERSON> config từ preStageId và activeStageId<br>- Kiểm tra tồn tại và không trùng lặp |
| 3 | `Main()` | Kiểm tra quyền và role | - Nếu role = "all" → bypass<br>- Nếu không → kiểm tra role của user |
| 4 | `Update()` | Cập nhật entity chính | - Lấy primary entity từ workflow<br>- Kiểm tra record không inactive |
| 5 | `Update()` | Xử lý thông tin approver | - Nếu IsSaveInfoApprover = true<br>- Lưu approver và approve date |
| 6 | `Update()` | Kiểm tra tài liệu đính kèm | - Nếu CheckDoc = true<br>- Kiểm tra có attachment trong Note |
| 7 | `Update()` | Xử lý final step | - Nếu FinalStep = true<br>- Set process về Inactive/Finished |
| 8 | `ChangeStage()` | Thay đổi stage trong BPF | - Lấy process instances<br>- Xác định vị trí stage hiện tại và trước đó |
| 9 | `ChangeStage()` | Xử lý back process | - Nếu preStagePosition > activeStagePosition<br>- Set lại stage trước đó |
| 10 | `Update()` | Cấp quyền truy cập | - Grant access cho các user trong userIds |

## **Định Hướng và Mục Đích**

### **1. Mục đích chính:**
- **Quản lý Business Process Flow (BPF)** trong Microsoft Dynamics 365
- **Tự động hóa việc chuyển stage** và cập nhật trạng thái record
- **Kiểm soát quyền truy cập** và phê duyệt theo từng stage

### **2. Các tính năng chính:**

#### **Kiểm tra quyền role:**
```csharp
var roleResults = serviceAdmin.RetrieveMultiple(new QueryBuilder().CheckRole(userId, preRoleName.Split(Constants.Separator))).Entities.ToList();
if (roleResults.Count == 0)
{
    throw new InvalidPluginExecutionException(Constants.ErrorMessage.PermissionDenied);
}
```

#### **Lưu thông tin người phê duyệt:**
```csharp
if (model.IsSaveInfoApprover)
{
    if (!string.IsNullOrWhiteSpace(model.ApproverColumn))
    {
        update[model.ApproverColumn] = new EntityReference(Entities.SystemUser.LogicalName, model.CallingUserId);
    }
    if (!string.IsNullOrWhiteSpace(model.ApproveDateColumn))
    {
        update[model.ApproveDateColumn] = DateTime.UtcNow;
    }
}
```

#### **Kiểm tra tài liệu đính kèm:**
```csharp
if (model.CheckDoc)
{
    var results = service.RetrieveMultiple(new QueryBuilder().CheckAttachFileInNote(update.Id)).Entities.ToList();
    if (results.Count == 0) throw new InvalidPluginExecutionException(Constants.ErrorMessage.AttachFile);
}
```

#### **Xử lý final step:**
```csharp
if (model.FinalStep)
{
    service.Update(new Entity(target.LogicalName)
    {
        Id = target.Id,
        [Constants.CommonField.StateCode] = new OptionSetValue(Constants.OptionSet.StateCode.InActive),
        [Constants.CommonField.StatusCode] = new OptionSetValue(Constants.OptionSet.BusinessProcessFlowStatusCode.Finished)
    });
}
```

### **3. Điểm mạnh:**
- ✅ **Flexible Configuration**: Cấu hình linh hoạt qua ProcessConfig entity
- ✅ **Role-based Security**: Kiểm soát quyền theo role
- ✅ **Document Validation**: Kiểm tra tài liệu đính kèm
- ✅ **Audit Trail**: Lưu thông tin approver và thời gian
- ✅ **Back Process Support**: Hỗ trợ quay lại stage trước

### **4. Điểm cần cải thiện:**
- ⚠️ **Code có nhiều #if DISABLE blocks** - cần cleanup
- ⚠️ **Error handling** có thể được cải thiện
- ⚠️ **Performance**: Nhiều query riêng lẻ thay vì batch

### **5. Luồng hoạt động tổng quan:**
```
Entity Update (ActiveStage changed) 
→ Validate Config & Permissions 
→ Update Primary Record 
→ Handle Approver Info 
→ Check Documents 
→ Change BPF Stage 
→ Grant Access Rights
```

## **Cấu Hình Plugin**

### **Plugin Registration:**
- **Message**: Update
- **Entity**: Các entity có Business Process Flow
- **Event Pipeline Stage**: Post-operation
- **Execution Mode**: Synchronous
- **Image**: PreImage (bắt buộc)

### **ProcessConfig Entity Fields:**
- `Roles`: Danh sách role được phép (phân cách bằng separator)
- `ProcessStage`: Stage hiện tại
- `SetStateCode`: State code cần set
- `SetStatusCode`: Status code cần set
- `SetApprovalStatus`: Approval status cần set
- `IsSaveInfoAprover`: Có lưu thông tin approver không
- `ApproverColumn`: Tên field lưu approver
- `ApproveDateColumn`: Tên field lưu ngày approve
- `CheckDoc`: Có kiểm tra document không
- `FinalStep`: Có phải step cuối không

## **Error Messages**
- `PreProcessNotYetConfigured`: Chưa cấu hình process cho stage trước
- `ProcessNotYetConfigured`: Chưa cấu hình process cho stage hiện tại
- `DuplicateDetection`: Phát hiện cấu hình trùng lặp
- `PermissionDenied`: Không có quyền thực hiện
- `RecordInactive`: Record đã bị inactive
- `AttachFile`: Thiếu tài liệu đính kèm

## **Best Practices**
1. **Luôn test với nhiều scenario** khác nhau
2. **Cấu hình ProcessConfig** cẩn thận để tránh lỗi
3. **Kiểm tra quyền** trước khi deploy
4. **Monitor performance** khi có nhiều user đồng thời
5. **Backup data** trước khi thay đổi logic

## **Troubleshooting**
- **Plugin không chạy**: Kiểm tra registration và điều kiện trigger
- **Permission denied**: Kiểm tra role configuration
- **Performance chậm**: Optimize query trong QueryBuilder
- **Data không update**: Kiểm tra field mapping và entity reference

---
*Tài liệu được tạo từ phân tích code SetProcessFlow.cs*
