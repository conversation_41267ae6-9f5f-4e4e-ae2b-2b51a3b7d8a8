﻿using Microsoft.Crm.Sdk.Messages;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;

using System;

namespace C30Seeds.Plugin.ProcessConfig;

using Define;

public class SetProcessFlowFromMobile : IPlugin
{
    private readonly string _unsecureConfig;
    private readonly string _secureConfig;

    public SetProcessFlowFromMobile(string unsecureConfig, string secureConfig)
    {
        this._unsecureConfig = unsecureConfig;
        this._secureConfig = secureConfig;
    }

    public void Execute(IServiceProvider serviceProvider)
    {
        // Obtain the execution context from the service provider.
        if (serviceProvider.GetService(typeof(IPluginExecutionContext)) is not IPluginExecutionContext context) return;

        // Obtain the organization service reference.
        if (serviceProvider.GetService(typeof(IOrganizationServiceFactory)) is not IOrganizationServiceFactory factory) return;

        var service = factory.CreateOrganizationService(context.UserId);
        if (context.Depth > 3) return;

        var parentContext = context.ParentContext;
        // Obtain the target entity from the input parameters.
        if (context.InputParameters[Constants.Parameters.Target] is not Entity target) return;
        if ((target.GetAttributeValue<OptionSetValue>(Constants.CommonField.StatusCode)?.Value ?? -1) < 0) return;
        var preImg = context.PreEntityImages[Constants.Parameters.PreImage];
        context.SharedVariables.Add(Constants.CommonField.CallingUser, context.UserId);
        while (parentContext != null)
        {
            if (parentContext.MessageName.Equals(Constants.Message.PortalServices))
            {
                var retrieved = service.Retrieve(target.LogicalName, target.Id, new ColumnSet(Constants.CommonField.StateCode));

                var stateCode = retrieved.GetAttributeValue<OptionSetValue>(Constants.CommonField.StateCode)?.Value ?? -1;
                var preStateCode = preImg.GetAttributeValue<OptionSetValue>(Constants.CommonField.StateCode)?.Value ?? -1;

                if (stateCode != -1 && preStateCode != -1 && stateCode != Constants.OptionSet.StateCode.InActive && preStateCode == Constants.OptionSet.StateCode.InActive)
                {
                    throw new InvalidPluginExecutionException(Constants.ErrorMessage.RecordInactive);
                }

                // Get Process Instances
                if (service.Execute(new RetrieveProcessInstancesRequest
                {
                    EntityId = target.Id,
                    EntityLogicalName = target.LogicalName
                }) is not RetrieveProcessInstancesResponse processInstanceResponse) throw new Exception($"RetrieveProcessInstancesResponse: {Constants.ErrorMessage.ExecuteError}");

                // Declare variables to store values returned in response
#if DISABLE
                var processCount = processInstanceResponse.Processes.Entities.Count;
#endif
                var activeProcessInstance = processInstanceResponse.Processes.Entities[0]; // First record is the active process instance
                var activeProcessInstanceId = activeProcessInstance.Id; // Id of the active process instance, which will be used later to retrieve the active path of the process instance

                // Retrieve the process stages in the active path of the current process instance
                if (service.Execute(new RetrieveActivePathRequest
                {
                    ProcessInstanceId = activeProcessInstanceId
                }) is not RetrieveActivePathResponse activePathResponse) throw new Exception($"RetrieveActivePathRequest: {Constants.ErrorMessage.ExecuteError}");

                // Retrieve the active stage ID of in the active process instance
                var activeStageId = activeProcessInstance.GetAttributeValue<Guid>(Constants.CommonField.ProcessStageId);
#if DISABLE
                var activeStageName = "";
#endif
                var activeStagePosition = -1;

                for (var i = 0; i < activePathResponse.ProcessStages.Entities.Count; i++)
                {
                    // Retrieve the active stage name and active stage position based on the activeStageId for the process instance
                    if (activePathResponse.ProcessStages.Entities[i].GetAttributeValue<Guid>(Constants.CommonField.ProcessStageId).Equals(activeStageId))
                    {
#if DISABLE
                        activeStageName = pathResp.ProcessStages.Entities[i].Attributes[Constants.CommonField.ProcessStageName].ToString();
#endif
                        activeStagePosition = i;
                    }
                }

                // Retrieve the stage ID of the next stage that you want to set as active
                activeStageId = activePathResponse.ProcessStages.Entities[activeStagePosition + 1].GetAttributeValue<Guid>(Constants.CommonField.ProcessStageId);
                var entityName = string.Empty;
                if (!string.IsNullOrEmpty(_unsecureConfig))
                    entityName = _unsecureConfig;
                if (!string.IsNullOrEmpty(_secureConfig))
                    entityName = _secureConfig;

                if (entityName.Equals(string.Empty))
                {
                    throw new InvalidPluginExecutionException(Constants.ErrorMessage.PluginConfigurationNull);
                }

                var retrievedProcessInstance = service.Retrieve(entityName, activeProcessInstanceId, new ColumnSet(Constants.CommonField.ActiveStage, Constants.CommonField.StateCode));
                var stateCodeProcessInstance = retrievedProcessInstance.GetAttributeValue<OptionSetValue>(Constants.CommonField.StateCode)?.Value ?? -1;
                if (!stateCodeProcessInstance.Equals(Constants.OptionSet.StateCode.Active))
                {
                    return;
                }

                // Set the next stage as the active stage
                retrievedProcessInstance[Constants.CommonField.ActiveStage] = new EntityReference(Define.Entities.ProcessStage.LogicalName, activeStageId);
                service.Update(retrievedProcessInstance);
                break;
            }
            parentContext = parentContext.ParentContext;
        }
    }
}
