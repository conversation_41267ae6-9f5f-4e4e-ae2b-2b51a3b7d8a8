﻿using Microsoft.Xrm.Sdk.Query;

namespace C30Seeds.Extension.PluginRegistration.Shared;

internal class QueryBuilder
{
    internal QueryExpression GetAssemblyByName(string assemblyName)
    {
        return new QueryExpression
        {
            Distinct = true,
            NoLock = false,
            EntityName = Entities.PluginAssembly.LogicalName,
            ColumnSet = new ColumnSet(Entities.PluginAssembly.PrimaryKey),
            Criteria =
            {
                Conditions =
                {
                    new ConditionExpression
                    {
                        AttributeName = Entities.PluginAssembly.Name,
                        Operator = ConditionOperator.Equal,
                        Values =
                        {
                            assemblyName
                        }
                    }
                }
            }
        };
    }

    internal QueryExpression GetPluginTypeIdByAssemblyIdAndTypeName(object assemblyId, string pluginTypeName)
    {
        return new QueryExpression
        {
            Distinct = true,
            NoLock = false,
            EntityName = Entities.PluginType.LogicalName,
            ColumnSet = new ColumnSet(Entities.PluginType.PrimaryKey),
            Criteria =
            {
                Conditions =
                {
                    new ConditionExpression
                    {
                        AttributeName = Entities.PluginType.PluginAssemblyId,
                        Operator = ConditionOperator.Equal,
                        Values =
                        {
                            assemblyId
                        }
                    },
                    new ConditionExpression
                    {
                        AttributeName = Entities.PluginType.Name,
                        Operator = ConditionOperator.Equal,
                        Values =
                        {
                            pluginTypeName
                        }
                    }
                }
            }
        };
    }

    internal QueryExpression GetPluginStepId(object pluginTypeId)
    {
        return new QueryExpression
        {
            Distinct = true,
            NoLock = false,
            EntityName = Entities.SdkMessageProcessingStep.LogicalName,
            ColumnSet = new ColumnSet(Entities.SdkMessageProcessingStep.PrimaryKey, Entities.SdkMessageProcessingStep.Name),
            Criteria =
            {
                Conditions =
                {
                    new ConditionExpression
                    {
                        AttributeName = Entities.SdkMessageProcessingStep.PluginType,
                        Operator = ConditionOperator.Equal,
                        Values =
                        {
                            pluginTypeId
                        }
                    }
                }
            }
        };
    }

    internal QueryExpression GetSdkMessageId(string messageName)
    {
        return new QueryExpression
        {
            Distinct = true,
            NoLock = false,
            EntityName = Entities.SdkMessage.LogicalName,
            ColumnSet = new ColumnSet(Entities.SdkMessage.PrimaryKey),
            Criteria =
            {
                Conditions =
                {
                    new ConditionExpression
                    {
                        AttributeName = Entities.SdkMessage.Name,
                        Operator = ConditionOperator.Equal,
                        Values =
                        {
                            messageName
                        }
                    }
                }
            }
        };
    }

    internal QueryExpression GetSdkMessageFilterId(string entityName, object messageNameId)
    {
        return new QueryExpression
        {
            Distinct = true,
            NoLock = false,
            EntityName = Entities.SdkMessageFilter.LogicalName,
            ColumnSet = new ColumnSet(Entities.SdkMessageFilter.PrimaryKey),
            Criteria =
            {
                Conditions =
                {
                    new ConditionExpression
                    {
                        AttributeName = Entities.SdkMessageFilter.PrimaryObjectTypeCode,
                        Operator = ConditionOperator.Equal,
                        Values =
                        {
                            entityName
                        }
                    },
                    new ConditionExpression
                    {
                        AttributeName = Entities.SdkMessageFilter.MessageId,
                        Operator = ConditionOperator.Equal,
                        Values =
                        {
                            messageNameId
                        }
                    }
                }
            }
        };
    }

    internal QueryExpression GetAllSdkMessage(string pluginTypeId)
    {
        return new QueryExpression
        {
            Distinct = true,
            NoLock = false,
            EntityName = Entities.SdkMessageProcessingStepImage.LogicalName,
            ColumnSet = new ColumnSet(Entities.SdkMessageProcessingStepImage.PrimaryKey, Entities.SdkMessageProcessingStepImage.MessageProcessingStep),
            LinkEntities =
            {
                new LinkEntity
                {
                    LinkFromEntityName = Entities.SdkMessageProcessingStepImage.LogicalName,
                    LinkFromAttributeName = Entities.SdkMessageProcessingStepImage.MessageProcessingStep,
                    LinkToEntityName = Entities.SdkMessageProcessingStep.LogicalName,
                    LinkToAttributeName = Entities.SdkMessageProcessingStep.PrimaryKey,
                    LinkCriteria =
                    {
                        Conditions =
                        {
                            new ConditionExpression
                            {
                                AttributeName = Entities.SdkMessageProcessingStep.PluginType,
                                Operator = ConditionOperator.Equal,
                                Values =
                                {
                                    pluginTypeId
                                }
                            }
                        }
                    }
                }
            }
        };
    }
}
