﻿using Microsoft.Xrm.Sdk;

using System;
using System.IO;
using System.Runtime.Serialization.Json;
using System.Text;

namespace C30Seeds.Plugin.ProcessConfig;

public static class Helper
{
    public static T DeSerializeObject<T>(string json)
    {
        object obj = null!;
        var stream = new MemoryStream(Encoding.UTF8.GetBytes(json));
        var serializer = new DataContractJsonSerializer(typeof(T));
        obj = serializer.ReadObject(stream);
        stream.Close();
        return (T)obj;
    }

    public static TType GetFieldValue<TType>(this Entity entity, string attributeName, TType defaultValue = default!)
    {
        if (entity.TryGetAttributeValue<TType>(attributeName, out var value))
        {
            return value;
        }
        return defaultValue;
    }

    internal static string GenerateCode(this Entity entity, string variableName = "data")
    {
        var builder = new StringBuilder();
        builder.AppendLine($"var {variableName} = new Entity(\"{entity.LogicalName}\");");
        if (!entity.Id.Equals(Guid.Empty))
        {
            builder.AppendLine($"{variableName}.Id = Guid.Parse(\"{entity.Id}\");");
        }
        foreach (var attribute in entity.Attributes)
        {
            try
            {
                if (attribute.Value == null)
                {
                    builder.AppendLine($"{variableName}[\"{attribute.Key}\"] = null;");
                    continue;
                }

                object value = null!;
                if (attribute.Value.GetType().Name.Equals("Int32"))
                {
                    value = attribute.Value;
                }
                else if (attribute.Value.GetType().Name.Equals("Decimal"))
                {
                    value = $"{attribute.Value}m";
                }
                else if (attribute.Value.GetType().Name.Equals("Single"))
                {
                    value = $"{attribute.Value}f";
                }
                else if (attribute.Value.GetType().Name.Equals("Boolean"))
                {
                    value = Convert.ToBoolean(attribute.Value) ? "true" : "false";
                }
                else if (attribute.Value.GetType().Name.Equals("OptionSetValue"))
                {
                    var temp = ((OptionSetValue)attribute.Value);
                    if (temp != null) value = $"new OptionSetValue(Convert.ToInt32({temp.Value}))";
                }
                else if (attribute.Value.GetType().Name.Equals("DateTime"))
                {
                    var temp = (DateTime)attribute.Value;
                    value = $"new DateTime({temp.Year}, {temp.Month}, {temp.Day}, {temp.Hour}, {temp.Minute}, {temp.Second})";
                }
                else if (attribute.Value.GetType().Name.Equals("Money"))
                {
                    var temp = ((Money)attribute.Value);
                    if (temp != null) value = $"new Money(Convert.ToDecimal({temp.Value}))";
                }
                else if (attribute.Value.GetType().Name.Equals("EntityReference"))
                {
                    var temp = ((EntityReference)attribute.Value);
                    if (temp != null)
                        value = $"new EntityReference(\"{temp.LogicalName}\", Guid.Parse(\"{temp.Id}\"))";
                }
                else
                {
                    value = $"\"{attribute.Value}\"";
                }

                builder.AppendLine($"{variableName}[\"{attribute.Key}\"] = {value};");
            }
            catch (InvalidPluginExecutionException ex)
            {
                builder.AppendLine($"{attribute.Key} | {ex.Message} | {ex.InnerException.Message} | {ex.InnerException.StackTrace}");
            }
            catch (Exception ex)
            {
                builder.AppendLine($"{attribute.Key} | {ex.Message}");
            }
        }

        return builder.ToString();
    }
}
