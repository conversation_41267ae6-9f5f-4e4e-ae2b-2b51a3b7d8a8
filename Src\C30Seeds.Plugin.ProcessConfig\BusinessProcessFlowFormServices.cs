﻿using System;
using System.Linq;
using System.Xml;
using Microsoft.Crm.Sdk.Messages;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Messages;

namespace C30Seeds.Plugin.ProcessConfig;

using Entities = Define.Entities;
using Models;

internal class BusinessProcessFlowFormServices
{
    private readonly IOrganizationService _service;
    private readonly ExecuteMultipleRequest _multipleRequest;
    private readonly Entity _processConfig;
    
    public BusinessProcessFlowFormServices(IOrganizationService service, ExecuteMultipleRequest multipleRequest, Entity processConfig)
    {
        _service = service;
        _multipleRequest = multipleRequest;
        _processConfig = processConfig;
    }

    public void ApplyControl(string name)
    {
        var update = new Entity(_processConfig.LogicalName) { Id = _processConfig.Id };
        var targetStageId = _processConfig.GetFieldValue<EntityReference>(Entities.ProcessConfig.ProcessStage)?.Id ?? Guid.Empty;
        var controlParams = new ControlDescriptionParameters
        {
            DataFieldName = _processConfig.GetFieldValue<string>(Entities.ProcessConfig.PCF.BoundField),
            LocaleId = _processConfig.GetFieldValue<int>(Entities.ProcessConfig.PCF.Language),
            EnableFileAttachment = _processConfig.GetFieldValue(Entities.ProcessConfig.PCF.EnableFileAttachment, false),
            RequireFileAttachment = _processConfig.GetFieldValue(Entities.ProcessConfig.PCF.RequireFileAttachment, false),
            AllowedFileTypes = _processConfig.GetFieldValue<string>(Entities.ProcessConfig.PCF.AllowedFileTypes),
            MaxFileSizeInMB = _processConfig.GetFieldValue<int>(Entities.ProcessConfig.PCF.MaxFileSize),
            CustomText = _processConfig.GetFieldValue<string>(Entities.ProcessConfig.PCF.CustomText)
        };

        var xpathQuery = $"//tab[@id='{targetStageId:B}']//control[@datafieldname='{controlParams.DataFieldName}']";
        var bpfFormRecord = _service.RetrieveMultiple(new QueryBuilder().GetSystemFormByName(name)).Entities.FirstOrDefault();
        var bpfFormXml = bpfFormRecord.GetAttributeValue<string>(Entities.SystemForm.FormXml);

        if (!string.IsNullOrWhiteSpace(bpfFormXml) && !bpfFormXml.Contains("<hiddencontrols>"))
        {
            var document = new XmlDocument();
            document.LoadXml(bpfFormXml);
            CleanOrphanNodes(document);
            document.LoadXml(document.InnerXml);
            var controlNode = document.SelectSingleNode(xpathQuery);
            controlParams.CustomControlId = controlNode.Attributes["classid"].Value;
            if (controlNode.Attributes["uniqueid"]?.Value == null)
            {
                var controlGuid = Guid.NewGuid();
                update[Entities.ProcessConfig.PCF.ControlId] = controlGuid.ToString();
                controlParams.ForControlGuid = controlGuid.ToString("B");
                var uniqueIdAttr = controlNode.OwnerDocument.CreateAttribute("uniqueid");
                uniqueIdAttr.Value = controlParams.ForControlGuid;
                controlNode.Attributes.Append(uniqueIdAttr);
            }
            else
            {
                controlParams.ForControlGuid = controlNode.Attributes["uniqueid"].Value;
            }
            var controlDescriptionsNodes = controlNode.OwnerDocument.SelectSingleNode(".//controlDescriptions");
            if (controlDescriptionsNodes == null)
            {
                controlDescriptionsNodes = controlNode.OwnerDocument.CreateElement("controlDescriptions");
                controlNode.OwnerDocument.DocumentElement.AppendChild(controlDescriptionsNodes);
            }
            var ctrlDesElement = XmlGenerator.CreateControlDescriptionElement(controlDescriptionsNodes.OwnerDocument, controlParams);
            controlDescriptionsNodes.AppendChild(ctrlDesElement);
            _multipleRequest.Requests.Add(new UpdateRequest
            {
                Target = update
            });
            UpdateAndPublish(bpfFormRecord, document.InnerXml);
        }
    }

    public void RevertControl(string name)
    {
        var targetStageId = _processConfig.GetFieldValue<EntityReference>(Entities.ProcessConfig.ProcessStage)?.Id ?? Guid.Empty;
        var boundField = _processConfig.GetFieldValue<string>(Entities.ProcessConfig.PCF.BoundField);
        var xpathQuery = $"//tab[@id='{targetStageId:B}']//control[@datafieldname='{boundField}']";
        var bpfFormRecord = _service.RetrieveMultiple(new QueryBuilder().GetSystemFormByName(name)).Entities.FirstOrDefault();
        var bpfFormXml = bpfFormRecord.GetAttributeValue<string>(Entities.SystemForm.FormXml);
        if (!string.IsNullOrWhiteSpace(bpfFormXml) && !bpfFormXml.Contains("<hiddencontrols>"))
        {
            var document = new XmlDocument();
            document.LoadXml(bpfFormXml);
            CleanOrphanNodes(document);
            document.LoadXml(document.InnerXml);
            var controlNode = document.SelectSingleNode(xpathQuery);
            var controlDescriptionsNodes = document.SelectSingleNode(".//controlDescriptions");
            var controlDescriptionId = controlNode.Attributes["uniqueid"]?.Value ?? string.Empty;
            controlNode.Attributes.Remove(controlNode.Attributes["uniqueid"]);
            var xpathQueryCtrl = $"//controlDescription[@forControl='{controlDescriptionId}']";
            var controlDescriptionsNodeRemoved = controlDescriptionsNodes.OwnerDocument.SelectSingleNode(xpathQueryCtrl);
            controlDescriptionsNodes.RemoveChild(controlDescriptionsNodeRemoved);
            _multipleRequest.Requests.Add(new UpdateRequest
            {
                Target = new Entity(_processConfig.LogicalName)
                {
                    Id = _processConfig.Id,
                    [Entities.ProcessConfig.PCF.ControlId] = null
                }
            });
            UpdateAndPublish(bpfFormRecord, document.InnerXml);
        }
    }

    public void CleanOrphanNodes(XmlDocument document)
    {
        // Looking for orphan ConstrolDescriptions
        var controls = document.SelectNodes(".//control");
        var pcfNodes = document.SelectSingleNode(".//controlDescriptions");

        if (pcfNodes == null || controls == null)
            return;

        if (pcfNodes.ChildNodes.Count == 0)
        {
            document.InnerXml = document.InnerXml.Replace("<controlDescriptions></controlDescriptions>", "");
            return;
        }

        foreach (XmlNode pcfNode in pcfNodes.ChildNodes)
        {
            var uniqueId = pcfNode.Attributes["forControl"].Value;
            var relatedControlExist = controls.Cast<XmlNode>().Any(x => x.Attributes["uniqueid"]?.Value == uniqueId);

            if (!relatedControlExist || pcfNode.SelectNodes(".//parameters").Count == 0)
            {
                document.InnerXml = document.InnerXml.Replace(pcfNode.OuterXml, "");
            }
        }
    }

    public void UpdateAndPublish(Entity bpfFormRecord, string formXml)
    {
        var paramXml = string.Format("<importexportxml><entities><entity>{0}</entity></entities><nodes/><securityroles/><settings/><workflows/></importexportxml>", bpfFormRecord.GetAttributeValue<string>("objecttypecode"));
        _multipleRequest.Requests.Add(new UpdateRequest
        {
            Target = new Entity(bpfFormRecord.LogicalName)
            {
                Id = bpfFormRecord.Id,
                [Entities.SystemForm.FormXml] = formXml
            }
        });
        _multipleRequest.Requests.Add(new PublishXmlRequest
        {
            ParameterXml = paramXml
        });
    }
}
