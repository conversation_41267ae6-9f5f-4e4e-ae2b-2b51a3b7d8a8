﻿using Microsoft.Xrm.Sdk;

using System;
using System.Collections.Generic;

namespace C30Seeds.Plugin.ProcessConfig;

using Constants = Define.Constants;
using Entities = Define.Entities;
using Models;

public class SetProcessFlow : IPlugin
{
#if TRACE
    private ITracingService? _tracingService;
#endif

    /// <summary>
    /// Message: Update
    /// Entity:
    /// Event pipeline stage of execution: Post-operation
    /// Execution Mode: Synchronous
    /// Image: PreImage
    /// </summary>
    public void Execute(IServiceProvider serviceProvider)
    {
#if TRACE
        //Extract the tracing service for use in debugging sandboxed plug-ins.
        _tracingService = (ITracingService)serviceProvider.GetService(typeof(ITracingService));
        ProcessFlowCommon.InitializeTracing(_tracingService);
#endif
        // Obtain the execution context from the service provider.
        if (serviceProvider.GetService(typeof(IPluginExecutionContext)) is not IPluginExecutionContext context) return;

        // Obtain the organization service reference.
        if (serviceProvider.GetService(typeof(IOrganizationServiceFactory)) is not IOrganizationServiceFactory factory) return;
        var service = factory.CreateOrganizationService(context.UserId);
        var serviceAdmin = factory.CreateOrganizationService(null);

        if (context.Depth > 1) return;

        // Obtain the target entity from the input parameters.
        if (context.InputParameters[Constants.Parameters.Target] is not Entity target) return;
        if (context.PreEntityImages[Constants.Parameters.PreImage] is not Entity preImg) return;

        if (context.MessageName.ToUpper().Equals(Constants.Message.Update.ToUpper()) && target.Contains(Constants.CommonField.ActiveStage))
        {
#if TRACE
            Trace($"EntityName: {target.LogicalName}");
            Trace($"EnityId: {target.Id}");
            Trace($"ActiveStageLogicalName: {target.GetAttributeValue<EntityReference>(Constants.CommonField.ActiveStage).LogicalName}");
            Trace($"ActiveStageId: {target.GetAttributeValue<EntityReference>(Constants.CommonField.ActiveStage).Id}");
            Trace($"PreActiveStageId: {preImg.GetAttributeValue<EntityReference>(Constants.CommonField.ActiveStage).Id}");
#endif
            try
            {
                Main(service, serviceAdmin, target, context.UserId, preImg.GetAttributeValue<EntityReference>(Constants.CommonField.ActiveStage).Id);
            }
            catch (Exception ex)
            {
#if TRACE
                Trace($"Error in SetProcessFlow plugin: {ex.Message}");
#endif
                throw new InvalidPluginExecutionException($"SetProcessFlow error: {ex.Message}", ex);
            }
        }
    }

    public void Main(IOrganizationService service, IOrganizationService serviceAdmin, Entity target, Guid userId, Guid preStageId)
    {
        // Step 1: Validate process configuration and permissions using common library
        var validationResult = ProcessFlowCommon.ValidateProcessConfig(service, serviceAdmin, target, userId, preStageId);
        if (!validationResult.Success)
        {
            throw new InvalidPluginExecutionException(validationResult.Message);
        }

        var validationData = (ProcessFlowValidationData)validationResult.Data;
        var processConfig = validationData.ProcessConfig;
        var processConfigResults = validationData.ProcessConfigResults;
        var preRoleName = validationData.PreRoleName;

        // Step 2: Build process config model using common library
        var model = ProcessFlowCommon.BuildProcessConfigModel(processConfigResults, userId);

        // Step 3: Get next step users
        var userIds = new List<Guid>();
        var businessUnitNextStep = Guid.Empty; // This would need business unit configuration logic

        if (!preRoleName.ToLower().Equals(Constants.KeyForAllRole))
        {
            var roleName = processConfigResults.GetAttributeValue<string>(Entities.ProcessConfig.Roles);
            userIds = ProcessFlowCommon.GetNextStepUserIds(serviceAdmin, roleName, businessUnitNextStep);
        }

        // Step 4: Execute the update using refactored method
        Update(service, serviceAdmin, target, preStageId, processConfigResults, model, userIds);
    }

    public void Update(IOrganizationService service, IOrganizationService serviceAdmin, Entity target, Guid preStageId, Entity config, ProcessConfigModel model, List<Guid> userIds)
    {
#if TRACE
        Trace($"target.LogicalName: {target.LogicalName}");
        Trace($"Update Target: {target.Id}");
        Trace($"State code: {model.StateCode}");
        Trace($"Status code: {model.StatusCode}");
        Trace($"Stage name: {model.StageName}");
        Trace($"IsSaveInfoApprover: {model.IsSaveInfoApprover}");
        Trace($"ApproverColumn: {model.ApproverColumn}");
        Trace($"ApproveDateColumn: {model.ApproveDateColumn}");
        Trace($"checkDoc: {model.CheckDoc}");
        Trace($"finalStep: {model.FinalStep}");
#endif

        // Step 1: Get primary entity information using common library
        var primaryEntityResult = ProcessFlowCommon.GetPrimaryEntityInfo(serviceAdmin, target);
        if (!primaryEntityResult.Success)
        {
            throw new InvalidPluginExecutionException(primaryEntityResult.Message);
        }

        var primaryEntityInfo = (PrimaryEntityInfo)primaryEntityResult.Data;
        var update = new Entity(primaryEntityInfo.LogicalName)
        {
            Id = primaryEntityInfo.RecordId
        };

#if TRACE
        Trace($"logicalName: {primaryEntityInfo.LogicalName}");
        Trace($"recordId: {update.Id}");
#endif

        // Step 2: Check document attachment using common library
        var docCheckResult = ProcessFlowCommon.CheckDocumentAttachment(service, update.Id, model.CheckDoc);
        if (!docCheckResult.Success)
        {
            throw new InvalidPluginExecutionException(docCheckResult.Message);
        }

        // Step 3: Handle final step using common library
        var finalStepResult = ProcessFlowCommon.HandleFinalStep(service, target, model.FinalStep);
        if (!finalStepResult.Success)
        {
            throw new InvalidPluginExecutionException(finalStepResult.Message);
        }

        // Step 4: Update entity with approver information using common library
        var updateResult = ProcessFlowCommon.UpdateEntityWithApprover(update, model);
        if (!updateResult.Success)
        {
            throw new InvalidPluginExecutionException(updateResult.Message);
        }

        // Step 5: Change stage using common library
        var changeStageResult = ProcessFlowCommon.ChangeStage(serviceAdmin, primaryEntityInfo.Retrieved.LogicalName, ref update, preStageId, config, model);
        if (!changeStageResult.Success)
        {
            throw new InvalidPluginExecutionException(changeStageResult.Message);
        }

        // Step 6: Update the entity
        service.Update(update);

        // Step 7: Grant user access using common library
        if (userIds.Count > 0)
        {
            var grantAccessResult = ProcessFlowCommon.GrantUserAccess(serviceAdmin, update.ToEntityReference(), userIds);
            if (!grantAccessResult.Success)
            {
                throw new InvalidPluginExecutionException(grantAccessResult.Message);
            }
        }
    }



#if TRACE
    private void Trace(string format, params object[] args)
    {
        if (_tracingService is null)
        {
            Console.Write(format, args);
        }
        else
        {
            _tracingService.Trace(format, args);
        }
    }
#endif
}
