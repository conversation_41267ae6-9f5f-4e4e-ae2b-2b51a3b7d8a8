﻿using Microsoft.Crm.Sdk.Messages;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;

using System;
using System.Collections.Generic;
using System.Linq;

namespace C30Seeds.Plugin.ProcessConfig;

using Constants = Define.Constants;
using Entities = Define.Entities;
using Models;

public class SetProcessFlow : IPlugin
{
#if TRACE
    private ITracingService? _tracingService;
#endif

    /// <summary>
    /// Message: Update
    /// Entity:
    /// Event pipeline stage of execution: Post-operation
    /// Execution Mode: Synchronous
    /// Image: PreImage
    /// </summary>
    public void Execute(IServiceProvider serviceProvider)
    {
#if TRACE
        //Extract the tracing service for use in debugging sandboxed plug-ins.
        _tracingService = (ITracingService)serviceProvider.GetService(typeof(ITracingService));
#endif
        // Obtain the execution context from the service provider.
        if (serviceProvider.GetService(typeof(IPluginExecutionContext)) is not IPluginExecutionContext context) return;

        // Obtain the organization service reference.
        if (serviceProvider.GetService(typeof(IOrganizationServiceFactory)) is not IOrganizationServiceFactory factory) return;
        var service = factory.CreateOrganizationService(context.UserId);
        var serviceAdmin = factory.CreateOrganizationService(null);

        if (context.Depth > 1) return;

        // Obtain the target entity from the input parameters.
        if (context.InputParameters[Constants.Parameters.Target] is not Entity target) return;
        if (context.PreEntityImages[Constants.Parameters.PreImage] is not Entity preImg) return;

        if (context.MessageName.ToUpper().Equals(Constants.Message.Update.ToUpper()) && target.Contains(Constants.CommonField.ActiveStage))
        {
#if TRACE
            Trace($"EntityName: {target.LogicalName}");
            Trace($"EnityId: {target.Id}");
            Trace($"ActiveStageLogicalName: {target.GetAttributeValue<EntityReference>(Constants.CommonField.ActiveStage).LogicalName}");
            Trace($"ActiveStageId: {target.GetAttributeValue<EntityReference>(Constants.CommonField.ActiveStage).Id}");
            Trace($"PreActiveStageId: {preImg.GetAttributeValue<EntityReference>(Constants.CommonField.ActiveStage).Id}");
#endif
            Main(service, serviceAdmin, target, context.UserId, preImg.GetAttributeValue<EntityReference>(Constants.CommonField.ActiveStage).Id);
        }
    }

    public void Main(IOrganizationService service, IOrganizationService serviceAdmin, Entity target, Guid userId, Guid preStageId)
    {
        var processConfig = service.RetrieveMultiple(new QueryBuilder().GetProcessConfigByStageId(preStageId)).Entities.ToList();
        var activeStageId = target.GetAttributeValue<EntityReference>(Constants.CommonField.ActiveStage)?.Id ?? Guid.Empty;
        var processConfigResults = service.RetrieveMultiple(new QueryBuilder().GetProcessConfigByStageId(activeStageId)).Entities.ToList();
        if (processConfig.Count == 0)
        {
            throw new InvalidPluginExecutionException(Constants.ErrorMessage.PreProcessNotYetConfigured);
        }
        if (processConfigResults.Count == 0)
        {
            throw new InvalidPluginExecutionException(Constants.ErrorMessage.ProcessNotYetConfigured);
        }
        if (processConfigResults.Count > 1 || processConfig.Count > 1)
        {
            throw new InvalidPluginExecutionException(Constants.ErrorMessage.DuplicateDetection);
        }

        ProcessConfigModel model = new()
        {
            CallingUserId = userId
        };
        var businessUnitNextStep = Guid.Empty;
#if DISABLE
            var businessUnitPreStep = Guid.Empty;
#endif
        var userIds = new List<Guid>();
        var preRoleName = processConfig.First().GetAttributeValue<string>(Entities.ProcessConfig.Roles);

#if DISABLE
            int buConfigurePreStep = 0;
            if (processConfig.First().Contains(Entities.ProcessConfig.BusinessUnitConfigure.Name))
            {
                buConfigurePreStep = processConfig.First().GetAttributeValue<OptionSetValue>(Entities.ProcessConfig.BusinessUnitConfigure.Name)?.Value ?? 0;
            }
            if (buConfigurePreStep == Entities.ProcessConfig.BusinessUnitConfigure.Value.CurrentBusinessUnit)
            {
                businessUnitPreStep = currentBusinessUnit;
            }
            else if (buConfigurePreStep == Entities.ProcessConfig.BusinessUnitConfigure.Value.ParentBusinessUnit)
            {
                businessUnitPreStep = processConfig.First().GetAttributeValue<EntityReference>(Entities.ProcessConfig.ParentBusinessUnit)?.Id ?? Guid.Empty;
            }
            else if (buConfigurePreStep == Entities.ProcessConfig.BusinessUnitConfigure.Value.SpecialBusinessUnit)
            {
                businessUnitPreStep = processConfig.First().GetAttributeValue<EntityReference>(Entities.ProcessConfig.SpecialBusinessUnit)?.Id ?? Guid.Empty;
            }
#endif
        var roleName = processConfigResults.First().GetAttributeValue<string>(Entities.ProcessConfig.Roles);
        model.StageName = processConfigResults.First().GetAttributeValue<EntityReference>(Entities.ProcessConfig.ProcessStage).Name;

#if DISABLE
            int buConfigure = 0;
            if (processConfigResults.First().Contains(Entities.ProcessConfig.BusinessUnitConfigure.Name))
                buConfigure = processConfigResults.First().GetAttributeValue<OptionSetValue>(Entities.ProcessConfig.BusinessUnitConfigure.Name)?.Value ?? 0;
            if (buConfigure == Entities.ProcessConfig.BusinessUnitConfigure.Value.CurrentBusinessUnit)
            {
                businessUnitNextStep = currentBusinessUnit;
            }
            else if (buConfigure == Entities.ProcessConfig.BusinessUnitConfigure.Value.ParentBusinessUnit)
            {
                businessUnitNextStep = processConfigResults.First().GetAttributeValue<EntityReference>(Entities.ProcessConfig.ParentBusinessUnit)?.Id ?? Guid.Empty;
            }
            else if (buConfigure == Entities.ProcessConfig.BusinessUnitConfigure.Value.SpecialBusinessUnit)
            {
                businessUnitNextStep = processConfigResults.First().GetAttributeValue<EntityReference>(Entities.ProcessConfig.SpecialBusinessUnit)?.Id ?? Guid.Empty;
            }
#endif
        model.StateCode = processConfigResults.First().GetAttributeValue<int>(Entities.ProcessConfig.SetStateCode);
        model.StatusCode = processConfigResults.First().GetAttributeValue<int>(Entities.ProcessConfig.SetStatusCode);
        model.ApprovalStatus = processConfigResults.First().Contains(Entities.ProcessConfig.SetApprovalStatus) ? processConfigResults.First().GetAttributeValue<int>(Entities.ProcessConfig.SetApprovalStatus) : -1;
        model.IsSaveInfoApprover = processConfigResults.First().GetAttributeValue<bool>(Entities.ProcessConfig.IsSaveInfoAprover);
        model.ApproverColumn = processConfigResults.First().GetAttributeValue<string>(Entities.ProcessConfig.ApproverColumn);
        model.ApproveDateColumn = processConfigResults.First().GetAttributeValue<string>(Entities.ProcessConfig.ApproveDateColumn);
        model.IsSaveStageName = processConfigResults.First().GetAttributeValue<bool>(Entities.ProcessConfig.IsSaveStageName);
        model.StageNameColumn = processConfigResults.First().GetAttributeValue<string>(Entities.ProcessConfig.StageNameColumn);
        model.CheckDoc = processConfigResults.First().GetAttributeValue<bool>(Entities.ProcessConfig.CheckDoc);
        model.FinalStep = processConfigResults.First().GetAttributeValue<bool>(Entities.ProcessConfig.FinalStep);

        if (preRoleName.ToLower().Equals(Constants.KeyForAllRole))
        {
            Update(service, serviceAdmin, target, preStageId, processConfigResults.FirstOrDefault(), model, userIds);
        }
        else
        {
            var roleResults = serviceAdmin.RetrieveMultiple(new QueryBuilder().CheckRole(userId, preRoleName.Split(Constants.Separator))).Entities.ToList();
            if (roleResults.Count == 0)
            {
                throw new InvalidPluginExecutionException(Constants.ErrorMessage.PermissionDenied);
            }
            else
            {
                for (var i = 0; i < roleResults.Count; i++)
                {
                    if (!businessUnitNextStep.Equals(Guid.Empty))
                    {
                        userIds.AddRange([.. serviceAdmin.RetrieveMultiple(new QueryBuilder().FindUserByBuAndRole(businessUnitNextStep, roleName.Split(Constants.Separator))).Entities.ToList().Select(x => x.Id)]);
                    }
                }

                Update(service, serviceAdmin, target, preStageId, processConfigResults.FirstOrDefault(), model, userIds);
            }
        }
    }

    public void Update(IOrganizationService service, IOrganizationService serviceAdmin, Entity target, Guid preStageId, Entity config, ProcessConfigModel model, List<Guid> userIds)
    {
        //string prefix = "process";
#if TRACE
        Trace($"target.LogicalName: {target.LogicalName}");
        Trace($"Update Target: {target.Id}");
        Trace($"State code: {model.StateCode}");
        Trace($"Status code: {model.StatusCode}");
        Trace($"Stage name: {model.StageName}");
        Trace($"IsSaveInfoApprover: {model.IsSaveInfoApprover}");
        Trace($"ApproverColumn: {model.ApproverColumn}");
        Trace($"ApproveDateColumn: {model.ApproveDateColumn}");
        Trace($"checkDoc: {model.CheckDoc}");
        Trace($"finalStep: {model.FinalStep}");
#endif

        //string logicalName = target.LogicalName.Replace(prefix, "");
        var logicalName = serviceAdmin.RetrieveMultiple(new QueryBuilder().GetPrimaryEntityName(target.LogicalName)).Entities.FirstOrDefault()?.GetAttributeValue<string>(Entities.Workflow.PrimaryEntity);

#if TRACE
        Trace($"logicalName: {logicalName}");
#endif

        var fieldId = Constants.Template.BusinessFlowProcessId.Replace(Constants.Replacement.EntityName, logicalName);
#if TRACE
        Trace($"fieldId: {fieldId}");
#endif

        var retrieved = serviceAdmin.Retrieve(target.LogicalName, target.Id, new ColumnSet(fieldId));
        var temp = retrieved.GetAttributeValue<EntityReference>(fieldId)?.Id ?? Guid.Empty;

#if TRACE
        Trace($"All field in target: {string.Join(",", target.Attributes.Select(x => x.Key))}");
        Trace($"All field in retrieved: {string.Join(",", retrieved.Attributes.Select(x => x.Key))}");
        Trace($"Guid retrieved: {temp}");
#endif

        //if (!target.Contains(fieldId))
        //    fieldId = fieldId.Replace("_c30seeds", "");

        if (temp.Equals(Guid.Empty))
        {
            return;
        }
        var update = new Entity(logicalName)
        {
            Id = temp //target.GetAttributeValue<EntityReference>(fieldId).Id;
        };

        var refRecord = retrieved.GetAttributeValue<EntityReference>(fieldId);

        if (refRecord != null)
        {
            var record = serviceAdmin.Retrieve(refRecord.LogicalName, refRecord.Id, new ColumnSet(Constants.CommonField.StateCode));
            var stateCode = record.GetAttributeValue<OptionSetValue>(Constants.CommonField.StateCode)?.Value ?? -1;
            if (stateCode != -1 && stateCode.Equals(Constants.OptionSet.StateCode.InActive))
            {
                throw new InvalidPluginExecutionException(Constants.ErrorMessage.RecordInactive);
            }
        }
#if TRACE
        Trace($"recordId: {update.Id}");
#endif
        if (model.IsSaveInfoApprover)
        {
            if (!string.IsNullOrWhiteSpace(model.ApproverColumn))
            {
                update[model.ApproverColumn] = new EntityReference(Entities.SystemUser.LogicalName, model.CallingUserId);
            }

            if (!string.IsNullOrWhiteSpace(model.ApproveDateColumn))
            {
                update[model.ApproveDateColumn] = DateTime.UtcNow;
            }
        }

        if (model.CheckDoc)
        {
            var results = service.RetrieveMultiple(new QueryBuilder().CheckAttachFileInNote(update.Id)).Entities.ToList();
            if (results.Count == 0) throw new InvalidPluginExecutionException(Constants.ErrorMessage.AttachFile);
        }

        if (model.FinalStep)
        {
            //Finish process
            //Change to service
            service.Update(new Entity(target.LogicalName)
            {
                Id = target.Id,
                [Constants.CommonField.StateCode] = new OptionSetValue(Constants.OptionSet.StateCode.InActive),
                [Constants.CommonField.StatusCode] = new OptionSetValue(Constants.OptionSet.BusinessProcessFlowStatusCode.Finished)
            });
        }

        update[Constants.CommonField.StateCode] = new OptionSetValue(model.StateCode);
        update[Constants.CommonField.StatusCode] = new OptionSetValue(model.StatusCode);
        if (model.ApprovalStatus > 0)
        {
            update[Constants.CommonField.ApprovalStatus] = new OptionSetValue(model.ApprovalStatus);
        }
#if ENABLE_SET_STAGE_NAME
            update[Constants.CommonField.StageName] = model.StageName;
#endif
        if (model.IsSaveStageName && !string.IsNullOrWhiteSpace(model.StageNameColumn) && !string.IsNullOrWhiteSpace(model.StageName))
        {
            update[model.StageNameColumn] = model.StageName;
        }

        ChangeStage(serviceAdmin, retrieved.LogicalName, ref update, preStageId, config, model);

        service.Update(update); //Change to service

        for (var i = 0; i < userIds.Count; i++)
        {
            var grantAccessRequest = new GrantAccessRequest
            {
                PrincipalAccess = new PrincipalAccess
                {
                    AccessMask = Constants.ReadWriteShare,
                    Principal = new EntityReference(Entities.SystemUser.LogicalName, userIds[i])
                },
                Target = update.ToEntityReference()
            };

            serviceAdmin.Execute(grantAccessRequest);
        }
    }

    private void ChangeStage(IOrganizationService service, string logicalName, ref Entity parentEntity, Guid preStageId, Entity config, ProcessConfigModel model)
    {
        // Get Process Instances
        if (service.Execute(new RetrieveProcessInstancesRequest
        {
            EntityId = parentEntity.Id,
            EntityLogicalName = parentEntity.LogicalName
        }) is not RetrieveProcessInstancesResponse processInstanceResponse) throw new Exception($"RetrieveProcessInstancesResponse: {Constants.ErrorMessage.ExecuteError}");

        // Declare variables to store values returned in response
#if DISABLE
        var processCount = processInstanceResponse.Processes.Entities.Count;
#endif
        var activeProcessInstance = processInstanceResponse.Processes.Entities.First(); // First record is the active process instance
        var activeProcessInstanceId = activeProcessInstance.Id; // Id of the active process instance, which will be used later to retrieve the active path of the process instance

        // Retrieve the active stage ID of in the active process instance
        var activeStageId = activeProcessInstance.GetAttributeValue<Guid>(Constants.CommonField.ProcessStageId);

        // Retrieve the process stages in the active path of the current process instance
        if (service.Execute(new RetrieveActivePathRequest
        {
            ProcessInstanceId = activeProcessInstanceId
        }) is not RetrieveActivePathResponse activePathResponse) throw new Exception($"RetrieveActivePathRequest: {Constants.ErrorMessage.ExecuteError}");

#if DISABLE
        var activeStageName = string.Empty;
#endif
        var preStageName = string.Empty;
        var activeStagePosition = -1;
        var preStagePosition = -1;

#if TRACE
        Trace($"{Constants.ErrorMessage.ProcessInstance}");
#endif
        for (var i = 0; i < activePathResponse.ProcessStages.Entities.Count; i++)
        {
            // Retrieve the active stage name and active stage position based on the activeStageId for the process instance
            if (activePathResponse.ProcessStages.Entities[i].GetAttributeValue<Guid>(Constants.CommonField.ProcessStageId).Equals(activeStageId))
            {
#if DISABLE
                activeStageName = activePathResponse.ProcessStages.Entities[i].GetAttributeValue<string>(Constants.CommonField.ProcessStageName).ToString();
#endif
                activeStagePosition = i;
            }

            if (activePathResponse.ProcessStages.Entities[i].GetAttributeValue<Guid>(Constants.CommonField.ProcessStageId).Equals(preStageId))
            {
                preStagePosition = i;
                preStageName = activePathResponse.ProcessStages.Entities[i].GetAttributeValue<string>(Constants.CommonField.ProcessStageName);
            }
        }

        if (preStagePosition - activeStagePosition >= 1) // back process
        {
            //activeStageID = (Guid)activePathResponse.ProcessStages.Entities[activeStagePosition - 1].Attributes["processstageid"];
            var hasApprovalStatus = config.TryGetAttributeValue<int>(Entities.ProcessConfig.SetPreviousApprovalStatus, out var approvalStatus);
            var hasStateCode = config.TryGetAttributeValue<int>(Entities.ProcessConfig.SetPreviousStateCode, out var stateCode);
            var hasStatusCode = config.TryGetAttributeValue<int>(Entities.ProcessConfig.SetPreviousStatusCode, out var statusCode);

            if (hasApprovalStatus)
            {
                parentEntity[Constants.CommonField.ApprovalStatus] = new OptionSetValue(approvalStatus);
            }

            if (hasStateCode)
            {
                parentEntity[Constants.CommonField.StateCode] = new OptionSetValue(stateCode);
            }

            if (hasStatusCode)
            {
                parentEntity[Constants.CommonField.StatusCode] = new OptionSetValue(statusCode);
            }
#if ENABLE_SET_STAGE_NAME
            parentEntity[Constants.CommonField.StageName] = preStageName;
#endif

            if (model.IsSaveStageName && !string.IsNullOrWhiteSpace(model.StageNameColumn) && !string.IsNullOrWhiteSpace(preStageName))
            {
                parentEntity[model.StageNameColumn] = preStageName;
            }

            // Retrieve the process instance record to update its active stage
            var retrievedProcessInstance = service.Retrieve(logicalName, activeProcessInstanceId, new ColumnSet(Constants.CommonField.ActiveStage));

            // Set the next stage as the active stage
            retrievedProcessInstance[Constants.CommonField.ActiveStage] = config[Entities.ProcessConfig.ProcessPreviousStage];
            service.Update(retrievedProcessInstance);
        }
    }

#if TRACE
    private void Trace(string format, params object[] args)
    {
        if (_tracingService is null)
        {
            Console.Write(format, args);
        }
        else
        {
            _tracingService.Trace(format, args);
        }
    }
#endif
}
