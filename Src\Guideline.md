# C30Seeds ProcessConfig - Complete Implementation Guideline

## **Project Overview**

Dự án C30Seeds ProcessConfig được thiết kế để quản lý Business Process Flow (BPF) trong Microsoft Dynamics 365 với 3 components chính:

1. **Plugin Architecture** - Xử lý tự động khi có thay đổi BPF stage
2. **Custom API** - Cho phép PCF Controls call trực tiếp để thực hiện next step
3. **Common Library** - Chứa logic dùng chung giữa Plugin và Custom API

## **Architecture Overview**

```
┌─────────────────────────────────────────────────────────────┐
│                    C30Seeds ProcessConfig                   │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Plugin        │  │   Custom API    │  │ PCF Control  │ │
│  │ SetProcessFlow  │  │SetProcessFlowAPI│  │ (Future)     │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
│           │                     │                   │       │
│           └─────────────────────┼───────────────────┘       │
│                                 │                           │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │            ProcessFlowCommonLib.cs                      │ │
│  │  • ValidateProcessConfig()                              │ │
│  │  • BuildProcessConfigModel()                            │ │
│  │  • GetPrimaryEntityInfo()                               │ │
│  │  • CheckDocumentAttachment()                            │ │
│  │  • UpdateEntityWithApprover()                           │ │
│  │  • HandleFinalStep()                                    │ │
│  │  • ChangeStage()                                        │ │
│  │  • GrantUserAccess()                                    │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## **File Structure**

```
C30Seeds.ProcessConfig/
├── Src/
│   ├── C30Seeds.Plugin.ProcessConfig/
│   │   ├── ProcessFlowCommonLib.cs          # ⭐ Common Library
│   │   ├── SetProcessFlow.cs                # ⭐ Plugin (Refactored)
│   │   ├── SetProcessFlowAPIs.cs            # ⭐ Custom API
│   │   ├── SetProcessFlowFromMobile.cs      # Mobile Plugin
│   │   ├── RegistrationServices.cs          # Registration Services
│   │   ├── QueryBuilder.cs                  # Query Helper
│   │   ├── Helper.cs                        # Utility Functions
│   │   ├── Models/
│   │   │   ├── ProcessConfigModel.cs        # Process Config Model
│   │   │   ├── RequestModel.cs              # Request Model
│   │   │   └── ControlDescriptionParameters.cs
│   │   ├── Define/
│   │   │   ├── Constants.cs                 # Constants
│   │   │   └── Entities/                    # Entity Definitions
│   │   └── BusinessProcessFlowFormServices.cs
│   ├── C30Seeds.Plugin.ProcessConfig.WebResources/
│   │   └── c30seeds.processconfig.js        # Web Resources
│   ├── C30Seeds.Extension.PluginRegistration.Shared/
│   │   └── [Shared Components]
│   ├── SetProcessFlow_Guide.md              # Original Guide
│   ├── SetProcessFlow_Implementation_Guide.md # Implementation Guide
│   └── Guideline.md                         # ⭐ This File
└── [Future PCF Control Project]/
```

## **Core Components Detail**

### **1. ProcessFlowCommonLib.cs** 
**Purpose**: Central library chứa tất cả business logic dùng chung

**Key Classes:**
```csharp
public static class ProcessFlowCommon
{
    // Core Methods
    public static ProcessFlowResult ValidateProcessConfig(...)
    public static ProcessConfigModel BuildProcessConfigModel(...)
    public static ProcessFlowResult GetPrimaryEntityInfo(...)
    public static ProcessFlowResult CheckDocumentAttachment(...)
    public static ProcessFlowResult UpdateEntityWithApprover(...)
    public static ProcessFlowResult HandleFinalStep(...)
    public static ProcessFlowResult ChangeStage(...)
    public static ProcessFlowResult GrantUserAccess(...)
    public static List<Guid> GetNextStepUserIds(...)
}

public class ProcessFlowResult
{
    public bool Success { get; set; }
    public string Message { get; set; }
    public object? Data { get; set; }
    
    public static ProcessFlowResult CreateSuccess(string message, object? data = null)
    public static ProcessFlowResult CreateError(string message)
}

public class ProcessFlowValidationData
{
    public Entity ProcessConfig { get; set; }
    public Entity ProcessConfigResults { get; set; }
    public string PreRoleName { get; set; }
}

public class PrimaryEntityInfo
{
    public string LogicalName { get; set; }
    public Guid RecordId { get; set; }
    public string FieldId { get; set; }
    public Entity Retrieved { get; set; }
}
```

### **2. SetProcessFlow.cs (Plugin)**
**Purpose**: Plugin được refactor để sử dụng ProcessFlowCommonLib

**Key Features:**
- Trigger: Update message trên BPF entities
- Event: Post-operation, Synchronous
- Image: PreImage required
- Sử dụng ProcessFlowCommonLib cho tất cả logic
- Error handling với InvalidPluginExecutionException

**Registration Info:**
```xml
<step>
    <name>SetProcessFlow</name>
    <mode>0</mode> <!-- Synchronous -->
    <rank>1</rank>
    <stage>40</stage> <!-- Post-operation -->
    <supporteddeployment>0</supporteddeployment> <!-- Server Only -->
    <message>Update</message>
    <primaryentity>[BPF Entity Name]</primaryentity>
    <images>
        <image>
            <name>PreImage</name>
            <type>0</type> <!-- PreImage -->
            <attributes>activestageid</attributes>
        </image>
    </images>
</step>
```

### **3. SetProcessFlowAPIs.cs (Custom API)**
**Purpose**: Custom API cho PCF Controls để thực hiện process flow changes

**Input Parameters:**
- `Target` (EntityReference) - Entity cần update
- `FromStage` (EntityReference) - Stage hiện tại  
- `ToStage` (EntityReference) - Stage đích

**Output Parameters:**
- `Success` (Boolean) - Kết quả thành công/thất bại
- `Message` (String) - Thông báo kết quả
- `Details` (String) - Chi tiết lỗi (nếu có)

**Usage from PCF:**
```javascript
// Call Custom API from PCF Control
const request = {
    Target: { 
        "@odata.type": "Microsoft.Dynamics.CRM.your_entity",
        "your_entityid": "target-entity-guid"
    },
    FromStage: {
        "@odata.type": "Microsoft.Dynamics.CRM.processstage", 
        "processstageid": "current-stage-guid"
    },
    ToStage: {
        "@odata.type": "Microsoft.Dynamics.CRM.processstage",
        "processstageid": "next-stage-guid"
    }
};

Xrm.WebApi.online.execute({
    name: "c30seeds_SetProcessFlow",
    Target: request.Target,
    FromStage: request.FromStage,
    ToStage: request.ToStage
}).then(function(result) {
    if (result.Success) {
        console.log("Success:", result.Message);
        // Handle success
    } else {
        console.error("Error:", result.Message);
        console.error("Details:", result.Details);
        // Handle error
    }
}).catch(function(error) {
    console.error("API Error:", error);
});
```

## **Configuration Requirements**

### **ProcessConfig Entity Fields:**
```csharp
// Required Fields
public const string Roles = "c30seeds_roles";                    // Role names (comma separated)
public const string ProcessStage = "c30seeds_processstage";      // Current stage reference
public const string SetStateCode = "c30seeds_setstatecode";      // State code to set
public const string SetStatusCode = "c30seeds_setstatuscode";    // Status code to set

// Optional Fields  
public const string SetApprovalStatus = "c30seeds_setapprovalstatus";
public const string IsSaveInfoAprover = "c30seeds_issaveinfoAprover";
public const string ApproverColumn = "c30seeds_approvercolumn";
public const string ApproveDateColumn = "c30seeds_approvedatecolumn";
public const string IsSaveStageName = "c30seeds_issavestagename";
public const string StageNameColumn = "c30seeds_stagenamecolumn";
public const string CheckDoc = "c30seeds_checkdoc";
public const string FinalStep = "c30seeds_finalstep";

// Back Process Fields
public const string SetPreviousApprovalStatus = "c30seeds_setpreviousapprovalstatus";
public const string SetPreviousStateCode = "c30seeds_setpreviousstatecode";
public const string SetPreviousStatusCode = "c30seeds_setpreviousstatuscode";
public const string ProcessPreviousStage = "c30seeds_processpreviousstage";
```

### **Constants Configuration:**
```csharp
// Key Constants
public const string KeyForAllRole = "all";                       // Bypass role check
public const char Separator = ',';                               // Role separator
public const AccessRights ReadWriteShare = AccessRights.ReadAccess | AccessRights.WriteAccess | AccessRights.ShareAccess;

// Error Messages
public const string PreProcessNotYetConfigured = "Pre-process not yet configured";
public const string ProcessNotYetConfigured = "Process not yet configured";
public const string DuplicateDetection = "Duplicate process configuration detected";
public const string PermissionDenied = "Permission denied";
public const string RecordInactive = "Record is inactive";
public const string AttachFile = "Required attachment file is missing";
```

## **Deployment Guide**

### **Step 1: Build and Package**
```bash
# Build the solution
dotnet build C30Seeds.Plugin.ProcessConfig\C30Seeds.Plugin.ProcessConfig.csproj --configuration Release

# Assembly location
C30Seeds.Plugin.ProcessConfig\bin\Release\net462\publish\C30Seeds.Plugin.ProcessConfig.dll
```

### **Step 2: Deploy Plugin Assembly**
1. Upload assembly to Dynamics 365
2. Register plugin steps for SetProcessFlow
3. Verify plugin registration

### **Step 3: Register Custom API**
```xml
<!-- Custom API Definition -->
<customapi>
    <uniquename>c30seeds_SetProcessFlow</uniquename>
    <displayname>Set Process Flow</displayname>
    <description>Custom API to handle process flow changes from PCF</description>
    <bindingtype>0</bindingtype> <!-- Global -->
    <isfunction>false</isfunction>
    <isprivate>false</isprivate>
    <plugintypeid>[SetProcessFlowAPIs Plugin Type ID]</plugintypeid>
</customapi>
```

### **Step 4: Configure ProcessConfig Records**
Create ProcessConfig records for each BPF stage with appropriate settings.

## **Testing Strategy**

### **Unit Testing ProcessFlowCommonLib:**
```csharp
[TestMethod]
public void ValidateProcessConfig_ValidInput_ReturnsSuccess()
{
    // Arrange
    var service = new Mock<IOrganizationService>();
    var target = new Entity("test_entity") { Id = Guid.NewGuid() };
    
    // Act
    var result = ProcessFlowCommon.ValidateProcessConfig(service.Object, service.Object, target, Guid.NewGuid(), Guid.NewGuid());
    
    // Assert
    Assert.IsTrue(result.Success);
}
```

### **Integration Testing:**
1. Test Plugin với existing BPF processes
2. Test Custom API từ Postman/PCF
3. Test permission scenarios
4. Test document validation
5. Test back process scenarios

### **PCF Integration Testing:**
```javascript
// Test Custom API call
function testProcessFlowAPI() {
    const testData = {
        Target: { /* test entity */ },
        FromStage: { /* current stage */ },
        ToStage: { /* next stage */ }
    };
    
    return Xrm.WebApi.online.execute({
        name: "c30seeds_SetProcessFlow",
        ...testData
    });
}
```

## **Error Handling & Troubleshooting**

### **Common Issues:**
1. **Permission Errors** - Check ProcessConfig.Roles configuration
2. **Missing Parameters** - Validate Custom API input parameters
3. **BPF Stage Errors** - Verify ProcessConfig entity setup
4. **Document Validation** - Check attachment requirements

### **Debugging Steps:**
1. Enable plugin tracing
2. Check Custom API execution logs  
3. Validate ProcessConfig entity data
4. Monitor plugin trace logs

### **Error Codes:**
- `PreProcessNotYetConfigured` - Missing config for previous stage
- `ProcessNotYetConfigured` - Missing config for current stage
- `DuplicateDetection` - Multiple configs found for same stage
- `PermissionDenied` - User lacks required role
- `RecordInactive` - Target record is inactive
- `AttachFile` - Required document missing

## **Future Enhancements**

### **PCF Control Integration:**
1. Create PCF Control project
2. Implement Custom API calls
3. Add UI for process flow management
4. Handle error responses appropriately

### **Performance Optimizations:**
1. Implement caching for ProcessConfig
2. Batch operations where possible
3. Optimize query performance
4. Add async processing for heavy operations

### **Additional Features:**
1. Workflow integration
2. Email notifications
3. Audit trail enhancements
4. Mobile app support

## **Maintenance Notes**

### **Code Organization:**
- Keep business logic in ProcessFlowCommonLib
- Plugin should be thin wrapper around common library
- Custom API should handle input/output transformation only
- Maintain consistent error handling patterns

### **Version Control:**
- Tag releases with version numbers
- Maintain changelog for breaking changes
- Document API changes
- Keep backward compatibility when possible

### **Monitoring:**
- Set up plugin performance monitoring
- Track Custom API usage
- Monitor error rates
- Review trace logs regularly

---

**Last Updated:** 2025-01-19  
**Version:** 1.0  
**Author:** C30Seeds Development Team
