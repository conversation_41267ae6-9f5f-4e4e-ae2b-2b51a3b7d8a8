﻿using Microsoft.Xrm.Sdk;
using System;
using System.Linq;

namespace C30Seeds.Extension.PluginRegistration.Shared;

public class PluginRegistration
{
    private readonly IOrganizationService _service;

    public PluginRegistration(IOrganizationService service)
    {
        _service = service;
    }

    private Guid GetPluginTypeId(string assemblyName, string pluginTypeName)
    {
        var pluginAssemblies = _service.RetrieveMultiple(new QueryBuilder().GetAssemblyByName(assemblyName));

        if (pluginAssemblies.Entities.Count == 0)
        {
            throw new Exception(Message.GetMessage[Message.AssemblyNotFound].Replace(Replacements.EntityName, assemblyName));
        }

        //ASSIGN ASSEMBLY ID TO VARIABLE
        var assemblyId = pluginAssemblies.Entities.First().Id;

        var pluginTypes = _service.RetrieveMultiple(new QueryBuilder().GetPluginTypeIdByAssemblyIdAndTypeName(assemblyId, pluginTypeName));

        if (pluginTypes.Entities.Count == 0)
        {
            return Guid.Empty;
        }

#if DISABLE
        //Check registered step
        var pluginSteps =
_service.RetrieveMultiple(new QueryBuilder().GetPluginStepId(pluginTypes.Entities.First().Id));

        return pluginSteps.Entities.Count == 0 ? pluginTypes.Entities.First().Id : Guid.Empty;
#endif

        return pluginTypes.Entities.First().Id;
    }

    private Guid GetSdkMessageId(string messageName)
    {
        var sdkMessages = _service.RetrieveMultiple(new QueryBuilder().GetSdkMessageId(messageName));

        if (sdkMessages.Entities.Count > 0)
        {
            return sdkMessages.Entities.First().Id;
        }
        throw new Exception(Message.GetMessage[Message.SdkMessageNotFound].Replace(Replacements.MessageName, messageName));
    }

    private Guid GetSdkMessageFilterId(string entityName, object messageNameId)
    {
        var sdkMessageFilters = _service.RetrieveMultiple(new QueryBuilder().GetSdkMessageFilterId(entityName, messageNameId));

        if (sdkMessageFilters.Entities.Count > 0)
        {
            return sdkMessageFilters.Entities.First().Id;
        }
        throw new Exception(Message.GetMessage[Message.SdkMessageFilterNotFound].Replace(Replacements.EntityName, entityName));
    }

    public Guid CreateSdkMessageStep(string assemblyName, string pluginTypeName, string messageName, string entityName, int mode, int stage, int rank, string unsecureConfiguration = null!)
    {
        var messageFilterId = Guid.Empty;
        var pluginTypeId = GetPluginTypeId(assemblyName, pluginTypeName);
        var messageId = GetSdkMessageId(messageName);
        if (string.IsNullOrWhiteSpace(entityName))
        {
            entityName = Constants.AnyEntity;
        }
        else
        {
            messageFilterId = GetSdkMessageFilterId(entityName, messageId);
        }

        if (!pluginTypeId.Equals(Guid.Empty) && !messageId.Equals(Guid.Empty))
        {
            return _service.Create(new Entity(Entities.SdkMessageProcessingStep.LogicalName)
            {
                [Entities.SdkMessageProcessingStep.Name] = Constants.Template.Description.Replace(Replacements.PluginTypeName, pluginTypeName).Replace(Replacements.MessageName, messageName).Replace(Replacements.EntityName, entityName),
                [Entities.SdkMessageProcessingStep.Description] = Constants.Template.Description.Replace(Replacements.PluginTypeName, pluginTypeName).Replace(Replacements.MessageName, messageName).Replace(Replacements.EntityName, entityName),
                [Entities.SdkMessageProcessingStep.Configuration] = unsecureConfiguration,
                [Entities.SdkMessageProcessingStep.InvocationSource.AttributeName] = new OptionSetValue(Entities.SdkMessageProcessingStep.InvocationSource.Value.Parent),
                [Entities.SdkMessageProcessingStep.SdkMessageId] = new EntityReference(Entities.SdkMessage.LogicalName, messageId),
                [Entities.SdkMessageProcessingStep.Deployment.AttributeName] = new OptionSetValue(Entities.SdkMessageProcessingStep.Deployment.Value.ServerOnly),
                [Entities.SdkMessageProcessingStep.PluginType] = new EntityReference(Entities.PluginType.LogicalName, pluginTypeId),
                [Entities.SdkMessageProcessingStep.Mode.AttributeName] = new OptionSetValue(mode),
                [Entities.SdkMessageProcessingStep.Rank] = rank,
                [Entities.SdkMessageProcessingStep.Stage.AttributeName] = new OptionSetValue(stage),
                [Entities.SdkMessageProcessingStep.SdkMessageFilterId] = messageFilterId.Equals(Guid.Empty) ? null : new EntityReference(Entities.SdkMessageFilter.LogicalName, messageFilterId)
            });
        }
        return Guid.Empty;
    }

    public Guid CreateImage(Guid stepId, int imageType, string imageName, params string[] attributes)
    {
        if (stepId.Equals(Guid.Empty)) return Guid.Empty;

        var data = string.Empty;
        if (attributes.Length > 0)
        {
            data = string.Join(",", attributes);
        }

        return _service.Create(new Entity(Entities.SdkMessageProcessingStepImage.LogicalName)
        {
            [Entities.SdkMessageProcessingStepImage.MessagePropertyName] = Constants.DefaultValues.MessagePropertyName,
            [Entities.SdkMessageProcessingStepImage.EntityAlias] = imageName,
            [Entities.SdkMessageProcessingStepImage.Name] = imageName,
            [Entities.SdkMessageProcessingStepImage.ImageType.AttributeName] = new OptionSetValue(imageType),
            [Entities.SdkMessageProcessingStepImage.Attributes] = data,
            [Entities.SdkMessageProcessingStepImage.MessageProcessingStep] = new EntityReference(Entities.SdkMessageProcessingStep.LogicalName, stepId)
        });
    }
}
