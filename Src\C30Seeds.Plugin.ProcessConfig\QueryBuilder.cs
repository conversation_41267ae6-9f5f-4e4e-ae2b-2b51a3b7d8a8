﻿using Microsoft.Xrm.Sdk.Query;

using System;

namespace C30Seeds.Plugin.ProcessConfig;

using Constants = Define.Constants;
using Entities = Define.Entities;

internal class QueryBuilder
{
    internal QueryExpression GetProcessConfigByStageId(Guid stageId)
    {
        return new QueryExpression()
        {
            Distinct = true,
            NoLock = true,
            EntityName = Entities.ProcessConfig.LogicalName,
            ColumnSet = new ColumnSet(Entities.ProcessConfig.PrimaryKey, Entities.ProcessConfig.Name, Entities.ProcessConfig.Roles, Entities.ProcessConfig.SetStateCode, Entities.ProcessConfig.SetStatusCode, Entities.ProcessConfig.SetApprovalStatus, Entities.ProcessConfig.ProcessStage, Entities.ProcessConfig.CheckDoc, Entities.ProcessConfig.IsSaveInfoAprover, Entities.ProcessConfig.ApproverColumn, Entities.ProcessConfig.ApproveDateColumn, Entities.ProcessConfig.IsSaveStageName, Entities.ProcessConfig.StageNameColumn, Entities.ProcessConfig.FinalStep, Entities.ProcessConfig.ProcessPreviousStage, Entities.ProcessConfig.SetPreviousStateCode, Entities.ProcessConfig.SetPreviousStatusCode, Entities.ProcessConfig.SetPreviousApprovalStatus),
            Criteria =
            {
                Conditions =
                {
                    new ConditionExpression
                    {
                        AttributeName = Entities.ProcessConfig.ProcessStage,
                        Operator = ConditionOperator.Equal,
                        Values =
                        {
                            stageId
                        }
                    }
                }
            }
        };
    }

    internal FetchExpression CheckRole(Guid userId, string[] roleName)
    {
        return new FetchExpression(CheckRoleQuery(userId, false, Guid.Empty, roleName));
    }

    internal FetchExpression CheckRole(Guid userId, Guid businessUnit, string[] roleName)
    {
        return new FetchExpression(CheckRoleQuery(userId, true, businessUnit, roleName));
    }

    private string CheckRoleQuery(Guid userId, bool haveBusinessUnit, Guid businessUnit, string[] roleName)
    {
        string fetch = @"<fetch distinct='true' no-lock='true'>";
        fetch += $@"<entity name='{Entities.Role.LogicalName}'>";
        fetch += $@"<attribute name='{Entities.Role.PrimaryKey}' />";
        fetch += $@"<attribute name='{Entities.Role.Name}' />";
        fetch += @"<filter type='and'>";
        fetch += $@"<condition attribute='{Entities.Role.Name}' operator='in'>";
        for (int i = 0; i < roleName.Length; i++)
        {
            fetch += $@"<value>{roleName[i]}</value>";
        }
        fetch += $@"</condition>";
        fetch += @"</filter>";
        fetch += $@"<link-entity name='{Constants.Relationships.SystemUserRoles}' from='{Entities.Role.PrimaryKey}' to='{Entities.Role.PrimaryKey}' intersect='true'>";
        fetch += $@"<link-entity name='{Entities.SystemUser.LogicalName}' from='{Entities.SystemUser.PrimaryKey}' to='{Entities.SystemUser.PrimaryKey}'>";
        fetch += @"<filter type='and'>";
        fetch += $@"<condition attribute='{Entities.SystemUser.PrimaryKey}' operator='eq' value='{userId}'/>";
        if (haveBusinessUnit)
        {
            fetch += $@"<condition attribute='{Entities.SystemUser.Department}' operator='eq' value='{businessUnit}'/>";
        }
        fetch += @"</filter>";
        fetch += @"</link-entity>";
        fetch += @"</link-entity>";
        fetch += @"</entity>";
        fetch += @"</fetch>";
        return fetch;
    }

    internal FetchExpression FindUserByBuAndRole(Guid businessUnitId, string[] roleName)
    {
        string fetch = @"<fetch distinct='true' no-lock='true'>";
        fetch += $@"<entity name='{Entities.SystemUser.LogicalName}'>";
        fetch += $@"<attribute name='{Entities.SystemUser.PrimaryKey}' />";
        fetch += $@"<link-entity name='{Constants.Relationships.SystemUserRoles}' from='{Entities.SystemUser.PrimaryKey}' to='{Entities.SystemUser.PrimaryKey}' intersect='true'>";
        fetch += $@"<link-entity name='{Entities.Role.LogicalName}' from='{Entities.Role.PrimaryKey}' to='{Entities.Role.PrimaryKey}' />";
        fetch += @"</link-entity>";
        fetch += @"<filter type='and'>";
        fetch += $@"<condition attribute='{Entities.SystemUser.Department}' operator='eq' value='{businessUnitId}'/>";
        fetch += $@"<condition entityname='{Entities.Role.LogicalName}' attribute='{Entities.Role.Name}' operator='in'>";
        for (int i = 0; i < roleName.Length; i++)
        {
            fetch += $@"<value>{roleName[i]}</value>";
        }
        fetch += $@"</condition>";
        fetch += @"</filter>";
        fetch += @"</entity>";
        fetch += @"</fetch>";
        return new FetchExpression(fetch);
    }

    internal FetchExpression CheckAttachFileInNote(Guid guid)
    {
        string fetch = $@"
              <fetch top='1' no-lock='true' >
                <entity name='{Entities.Annotation.LogicalName}' >
                    <attribute name='{Entities.Annotation.PrimaryKey}' />
                    <filter>
                        <condition attribute='{Entities.Annotation.DocumentBody}' operator='not-null' />
                        <condition attribute='{Entities.Annotation.ObjectId}' operator='eq' value='{guid}' />
                    </filter>
                </entity>
              </fetch>
            ";
        return new FetchExpression(fetch);
    }

    internal QueryExpression CheckRoles(Guid userId, string[] roleName)
    {
        return new QueryExpression()
        {
            Distinct = true,
            NoLock = true,
            EntityName = Entities.Role.LogicalName,
            ColumnSet = new ColumnSet(Entities.Role.PrimaryKey, Entities.Role.Name),
            LinkEntities =
            {
                new LinkEntity
                {
                    JoinOperator = JoinOperator.Inner,
                    LinkFromEntityName = Entities.Role.LogicalName,
                    LinkFromAttributeName = Entities.Role.PrimaryKey,
                    LinkToEntityName = Constants.Relationships.SystemUserRoles,
                    LinkToAttributeName = Entities.Role.PrimaryKey,
                    LinkEntities =
                    {
                        new LinkEntity
                        {
                            JoinOperator = JoinOperator.Inner,
                            LinkFromEntityName = Constants.Relationships.SystemUserRoles,
                            LinkFromAttributeName = Entities.SystemUser.PrimaryKey,
                            LinkToEntityName = Entities.SystemUser.LogicalName,
                            LinkToAttributeName = Entities.SystemUser.PrimaryKey,
                            LinkCriteria =
                            {
                                Conditions =
                                {
                                    new ConditionExpression
                                    {
                                        AttributeName = Entities.SystemUser.PrimaryKey,
                                        Operator = ConditionOperator.Equal,
                                        Values =
                                        {
                                            userId
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            Criteria =
            {
                Conditions =
                {
                    new ConditionExpression
                    {
                        AttributeName = Entities.Role.Name,
                        Operator = ConditionOperator.In,
                        Values =
                        {
                            roleName
                        }
                    }
                }
            }
        };
    }

    internal FetchExpression GetPrimaryEntityName(string entityName)
    {
        return new FetchExpression($@"
            <fetch distinct='false'>
              <entity name='{Entities.Workflow.LogicalName}'>
                <attribute name='{Entities.Workflow.PrimaryEntity}' />
                <order attribute='{Entities.Workflow.Name}' descending='false' />
                <filter type='and'>
                  <condition attribute='{Entities.Workflow.Type.Name}' operator='eq' value='{Entities.Workflow.Type.Value.Definition}'/>
                  <condition attribute='{Entities.Workflow.UniqueName}' operator='eq' value='{entityName}'/>
                  <filter type='and'>
                    <condition attribute='{Entities.Workflow.RenderObjectTypeCode}' operator='null' />
                    <filter type='or'>
                      <filter type='and'>
                        <condition attribute='{Entities.Workflow.Category.Name}' operator='eq' value='{Entities.Workflow.Category.Value.Dialog}'/>
                        <condition attribute='{Entities.Workflow.LanguageCode}' operator='eq-userlanguage' />
                      </filter>
                      <condition attribute='{Entities.Workflow.Category.Name}' operator='eq' value='{Entities.Workflow.Category.Value.BusinessProcessFlow}'/>
                    </filter>
                  </filter>
                </filter>
              </entity>
            </fetch>");
    }

    internal QueryExpression GetWorkflowUniqueName(Guid processId)
    {
        return new QueryExpression
        {
            Distinct = true,
            NoLock = false,
            EntityName = Entities.Workflow.LogicalName,
            ColumnSet = new ColumnSet(Entities.Workflow.UniqueName),
            LinkEntities =
            {
                new LinkEntity
                {
                    LinkFromEntityName = Entities.Workflow.LogicalName,
                    LinkFromAttributeName = Entities.Workflow.PrimaryKey,
                    LinkToEntityName = Entities.ProcessConfig.LogicalName,
                    LinkToAttributeName = Entities.ProcessConfig.Process,
                    LinkCriteria =
                    {
                        Conditions =
                        {
                            new ConditionExpression
                            {
                                AttributeName = Entities.ProcessConfig.Process,
                                Operator = ConditionOperator.Equal,
                                Values =
                                {
                                    processId
                                }
                            }
                        }
                    }
                }
            }
        };
    }

    internal QueryExpression GetAllProcessById(object processId)
    {
        return new QueryExpression
        {
            Distinct = true,
            NoLock = true,
            EntityName = Entities.ProcessConfig.LogicalName,
            ColumnSet = new ColumnSet(Entities.ProcessConfig.PrimaryKey, Entities.ProcessConfig.WithMobile, Entities.ProcessConfig.Registered, Entities.ProcessConfig.BusinessProcessFlowStep, Entities.ProcessConfig.BusinessProcessFlowStepImage, Entities.ProcessConfig.MobileStep, Entities.ProcessConfig.MobileStepImage),
            Criteria =
            {
                Conditions =
                {
                    new ConditionExpression
                    {
                        AttributeName = Entities.ProcessConfig.Process,
                        Operator = ConditionOperator.Equal,
                        Values =
                        {
                            processId
                        }
                    }
                }
            }
        };
    }

    internal QueryExpression GetCustomControl()
    {
        return new QueryExpression
        {
            EntityName = Entities.CustomControl.LogicalName,
            ColumnSet = new ColumnSet(Entities.CustomControl.CompatibleDataTypes, Entities.CustomControl.Manifest),
            Criteria =
            {
                Conditions =
                {
                    new ConditionExpression(Entities.CustomControl.PrimaryColumn, ConditionOperator.Equal, Constants.CustomControlName)
                }
            }
        };
    }

    internal QueryExpression GetSystemFormByName(string name)
    {
        return new QueryExpression
        {
            EntityName = Entities.SystemForm.LogicalName,
            ColumnSet = new ColumnSet(Entities.SystemForm.PrimaryColumn, Entities.SystemForm.FormXml, Entities.SystemForm.ObjectTypeCode),
            Criteria =
            {
                Conditions =
                {
                    new ConditionExpression(Entities.SystemForm.ObjectTypeCode, ConditionOperator.Like, name)
                }
            }
        };
    }
}
